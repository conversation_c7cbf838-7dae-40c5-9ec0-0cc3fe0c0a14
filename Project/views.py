from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
from datetime import datetime, timedelta
import hashlib
import hmac
import json
import os
import tempfile
import time
import random
from django.http import HttpResponse, JsonResponse
import requests
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.utils.timezone import now
from AI.comment_ai import generate_quick_reply, generate_social_comment
from AI.duplicate_post import create_complete_duplicate
from AI.generate_response import generate_media_content
from Authentication.jwt_auth import CustomJWTAuthentication
from OlaMaps.get_places import nearby_places, search_places
from RazorPay.payments import create_payment_link
from TikTok.tiktok_upload_video import tiktok_upload_videos
from Vimeo.get_vimeo_profile import get_vimeo_profile
from Vimeo.upload_vimeo import upload_vimeo_video
from Vimeo.vimeo_analytics import get_vimeo_analytics
from X.upload_tweet import post_tweet_with_asset, upload_video_and_post_tweet
from X.x_auth import get_and_update_x_token, get_x_user_info
from core import settings
from helpers.check_availability import check_multiple_roles_permissions, check_platform_posts_last_24h, check_user_plan, check_user_posts_24h
from helpers.check_story import is_more_than_24_hours_old
from helpers.countries_get import get_current_location
from helpers.email_regex import extract_organization_id, extract_vimeo_id
from helpers.encrypt import decrypt_data, encrypt_data
from helpers.extract_permission import extract_permission_keys
from helpers.generate_qr import generate_qr_code
from helpers.id_decode import decode_token
from helpers.image_h_w import get_image_dimensions
from helpers.load_data import process_and_store_data
from helpers.paginator import CustomPagination, ReelPagination, StoryPagination, UserProfilePagination
from Project.link_upload_post import create_linkedin_post_with_multiple_media, create_linkedin_post_with_video
from helpers.profile_completion import get_profile_completion_status
from helpers.schedule_checker import is_within_5_minutes
from helpers.onesignal_test import *
from kyc.service_in import kyc_generate_adhar_otp, kyc_verify_adhar_otp, verify_pan_details
from linkedin.get_linked_in_profile import get_linkedin_profile
from linkedin.linked_in_analytics import linked_in_analytics_comments, linked_in_analytics_likes
from linkedin.oragnization_linkedin import get_country_by_id, get_function_by_id, get_industry_by_id, get_linkedin_date_impression, get_linkedin_follower_statistics, get_linkedin_follower_stats, get_linkedin_organization_id, get_linkedin_share_statistics, get_seniority_by_id, linkedin_total_posts_count
from mastodon.image_upload import post_mastodon_status_with_images
from mastodon.video_upload import post_mastodon_video_status
from mastodon.mastodon_auth import get_mastodon_user_info
from mastodon.analytics import get_date_wise_post_counts, get_date_wise_like_counts, get_date_wise_reply_counts, get_date_wise_reblog_counts, get_total_mastodon_metrics
from meta.facebook_analytics import get_facebook_likes_and_comments
from meta.facebook_auth import get_facebook_user_info
from meta.facebook_chat import get_facebook_conversations, get_facebook_messages, send_facebook_media_message, send_facebook_message
from meta.facebook_final_analytics import get_facebook_daily_insights, get_facebook_page_metrics, get_facebook_share_url, get_post_count
from meta.facebook_story import upload_multiple_facebook_stories
from meta.facebook_uploads import upload_facebook_images, upload_facebook_videos
from meta.instagram_analytics import get_instagram_likes_and_comments, get_instagram_likes_and_comments_url, get_post_share_url
from meta.instagram_auth import get_instagram_user_info
from meta.instagram_chats import get_conversation_list, get_full_conversation_list, send_instagram_images_message, send_instagram_message
from meta.instagram_full_analytics import get_instagram_data, get_instagram_insights
from meta.instagram_story import upload_instagram_stories
from meta.instagram_uploads import create_carousel_post, upload_and_publish_instagram_video, upload_images_to_instagram
from meta.thread_analytics import get_post_share_threads_url, get_thread_likes_and_comments
from meta.thread_uploads import upload_and_publish_thread_video, upload_images_to_thread
from meta.threads_auth import get_threads_user_info, get_threads_user_profile_info
from meta.threads_full_analytics import get_threads_analytics_four, get_threads_analytics_one, get_threads_analytics_three, get_threads_analytics_two, threads_analytics_five
from mongodb.db import *
from pinterest.get_pin_profile import get_pinterest_profile
from pinterest.pinterest_analytics import get_analytics_data
from pinterest.pinterest_post_analytics import get_pinterest_post_analytics
from pinterest.upload_pin import post_pin
from reddit.reddit_auth import upload_photo_to_reddit
from reddit.reddit_profile import get_reddit_profile
from rnd.compress import compress_video, resize_image
from tumblr.shre_profile_tumblr import get_tumblr_profile_data
from tumblr.tumblr_analytics import get_tumblr_analytics
from tumblr.upload_tm_photo import upload_tumblr_photo, upload_tumblr_video
from helpers.schedule_checker import is_within_15_minutes
from youtube.upload_youtube import upload_video
from youtube.youtube_analytics import get_subscriber_count, get_videos_count, get_youtube_analytics
from youtube.youtube_stats import get_channel_details, get_youtube_stats
from telegram.telegram_auth import get_telegram_user_info, send_telegram_code, telegram_sign_in, telegram_logout
from telegram.telegram_chat import get_telegram_dialogs, send_telegram_message, get_telegram_user_chats

from .models import *
from .serializers import *
from core.settings import *
from django.db.models import Q, F, ExpressionWrapper, IntegerField, Count, Max , Prefetch , Exists , OuterRef , Value , Subquery
from django.db.models.functions import Concat
from django.core.files.base import ContentFile
from django.core.cache import cache
from django.core.files.storage import default_storage
from django.utils import timezone
from django.utils.html import escape
from django.views import View
from rest_framework.renderers import StaticHTMLRenderer

class PostCreateView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def post(self, request):
        try:
            domain = request.get_host()
            if '127.0.0.1' in domain:
                header = 'https://'
            else:
                header = 'https://'
        except ValueError:
            header = ''
            domain = ''
        try:
            auth_token = request.headers.get('Authorization')
            brand_id = request.headers.get('brand')
            user_id = decode_token(auth_token)
            subscription_id = request.headers.get('subscription')
            check_sub = check_user_plan(user_id,int(subscription_id))

            local_user_id = request.headers.get('user')
            role_status, role_permission_result = check_multiple_roles_permissions(local_user_id, brand_id)
            is_approved = True
            if role_status == False:
                is_approved = False
                # return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)

            if 8 not in role_permission_result:
                is_approved = False
                # return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)


            hashtags = request.data.get('hashtags')
            industry_id = request.data.get('industry')
            uploaded_files = request.FILES.getlist('upload_files') 
            thumbnail_files = request.FILES.getlist('thumbnail_files')

            serializer = PostSerializer(
                data=request.data, context={'request': request})
            is_any_post = check_user_posts_24h(brand_id)
            if serializer.is_valid():
                validated_data = serializer.validated_data
                title = validated_data.get('title')
                description = validated_data.get('description')
                location = validated_data.get('location')
                is_private = validated_data.get('is_private')
                is_video = validated_data.get('is_video')
                is_text_post = validated_data.get('is_text_post', False)
                facebook = validated_data.get('facebook',False)
                instagram = validated_data.get('instagram',False)
                linkedin = validated_data.get('linkedin',False)
                pinterest = validated_data.get('pinterest',False)
                vimeo = validated_data.get('vimeo',False)
                youtube = validated_data.get('youtube',False)
                dailymotion = validated_data.get('dailymotion',False)
                twitter = validated_data.get('twitter',False)
                mastadon = validated_data.get('mastadon',False)
                x = validated_data.get('x',False)
                tumblr = validated_data.get('tumblr',False)
                reddit = validated_data.get('reddit',False)
                tagged_in = validated_data.get('tagged_in')
                scheduled_at = validated_data.get('scheduled_at')
                is_posted = validated_data.get('is_posted', True)
                # is_posted = True
                is_scheduled = True

                if scheduled_at == None or scheduled_at == '':
                    is_scheduled = False

                if not is_posted:
                    is_scheduled = False
                    scheduled_at = None
                    print('Draft post being created')
                industry = None
                if industry_id:
                    try:
                        industry = PostIndustry.objects.get(pk=industry_id)
                    except PostIndustry.DoesNotExist:
                        return Response({'status': False, 'message': 'Industry not found'}, status=status.HTTP_400_BAD_REQUEST)

                post = Post.objects.create(
                    user_id=user_id,
                    brand_id=brand_id,
                    title=title,
                    description=description if description else '',
                    location=location if location else '',
                    is_private=is_private,
                    is_video=is_video,
                    is_text_post=is_text_post,
                    facebook=facebook,
                    instagram=instagram,
                    linkedin=linkedin,
                    pinterest=pinterest,
                    is_approved=is_approved,
                    vimeo=vimeo,
                    youtube=youtube,
                    dailymotion=dailymotion,
                    twitter=twitter,
                    mastadon=mastadon,
                    x=x,
                    tumblr=tumblr,
                    reddit=reddit,
                    tagged_in=tagged_in,
                    scheduled_at=scheduled_at,
                    is_posted=is_posted,
                    is_scheduled=is_scheduled,
                    industry=industry
                )
                if hashtags is not None:
                    hashtags = json.loads(hashtags)
                    for hashtag in hashtags:
                        try:
                            is_present = HashTags.objects.get(name=hashtag)
                            is_present.posts.append(post.pk)
                            is_present.save()
                        except HashTags.DoesNotExist:
                            create = HashTags.objects.create(
                                name=hashtag, posts=[post.pk])

                if tagged_in is not None:
                    for tags in tagged_in:
                        tagger = UserRegistration.objects.get(pk=user_id)
                        tagged_user = UserRegistration.objects.get(pk=tags)
                        onesignal_id = tagged_user.onesignal_player if tagged_user.onesignal_player else ''
                        if tagger == tagged_user:
                            pass
                        else:
                            Notification.objects.create(
                                user_id=tagged_user.pk,
                                type='tag',
                                from_user_id=user_id,
                                post_id=post.pk,
                                title='Flowkar',
                                message=f'You were tagged in a post by {tagger.name}'
                            )
                            # send_notification(
                            #     f'You were tagged in a post by {tagger.name}', 'Flowkar', [onesignal_id])
                            print('Notification Sent')
                image_formats = [
                    'jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'tif',
                    'webp', 'svg', 'ico', 'heif', 'heic', 'raw', 'nef',
                    'cr2', 'orf', 'arw', 'dng'
                ]
                video_formats = [
                    'mp4', 'mkv', 'mov', 'avi', 'flv', 'wmv', 'webm',
                    'm4v', 'mpg', 'mpeg', '3gp', '3g2', 'mts', 'm2ts',
                    'ts', 'ogv', 'rm', 'rmvb'
                ]
                if not is_text_post:
                    for file in uploaded_files:
                        file_name = str(file)
                        file_format = file_name.split('.')[-1]

                        temp_file_path = f"/tmp/{file_name}"
                        with open(temp_file_path, 'wb+') as temp_file:
                            for chunk in file.chunks():
                                temp_file.write(chunk)
                        
                        compressed_file_path = ''
                        resized_file_path = ''
                        
                        try:
                            if file_format.lower() in video_formats:
                                compressed_file_path = f"/tmp/compressed_{file_name}"
                                compress_video(
                                    temp_file_path, compressed_file_path, max_height=1920, max_width=1080)
                                
                                # Update the path to match what the function actually created (.mp4)
                                if not compressed_file_path.lower().endswith('.mp4'):
                                    base_path = compressed_file_path.rsplit('.', 1)[0] if '.' in compressed_file_path else compressed_file_path
                                    compressed_file_path = base_path + '.mp4'
                                
                                with open(compressed_file_path, 'rb') as compressed_file:
                                    # Save ContentFile with .mp4 extension
                                    compressed_filename = f"compressed_{file_name.split('.')[0]}.mp4"
                                    file = ContentFile(
                                        compressed_file.read(), name=compressed_filename)
                                is_video = True
                                
                            elif file_format.lower() in image_formats:
                                # Convert and resize image to JPEG
                                # Ensure the output path has a .jpeg extension
                                resized_file_path = f"/tmp/resized_{file_name.split('.')[0]}.jpeg"
                                resize_image(
                                    temp_file_path, resized_file_path, max_width=1920, max_height=1080
                                )
                                with open(resized_file_path, 'rb') as resized_file:
                                    file = ContentFile(
                                        # Save with .jpeg extension
                                        resized_file.read(), name=f"{file_name.split('.')[0]}.jpeg"
                                    )
                                is_video = False

                            PostFiles.objects.create(
                                post=post, file=file, is_video=is_video
                            )
                            
                        except Exception as e:
                            try:
                                post_to_delete = Post.objects.get(pk=post.pk)
                                post_to_delete.delete()
                            except Post.DoesNotExist:
                                pass
                            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
                        
                        finally:
                            # Clean up temporary files
                            if os.path.exists(temp_file_path):
                                os.remove(temp_file_path)
                            if compressed_file_path and os.path.exists(compressed_file_path):
                                os.remove(compressed_file_path)
                            if resized_file_path and os.path.exists(resized_file_path):
                                os.remove(resized_file_path)

                    all_post_files = PostFiles.objects.filter(
                        Q(post=post), Q(is_video=True))
                    if all_post_files and thumbnail_files is not None:
                        for post_file, thumbnail in zip(all_post_files, thumbnail_files):
                            print(thumbnail_files)
                            uploaded_thumbnail = PostFilesThumbnail.objects.create(
                                post=post_file.post, post_file=post_file, file=thumbnail)
                            print(
                                f'Thumbnail For --> {post_file.post.pk} , Thumbnail --> {uploaded_thumbnail.file.url}')

                    response_data = PostSerializer(
                        post, context={'request': request}).data
                    
                    if not is_posted:
                        # notify = send_notification(f'Post saved as draft', 'Flowkar',
                        #                     [post.user.onesignal_player])
                        # print(notify)
                        return Response({'status': True, 'message': 'Post saved as draft', 'data': response_data}, status=status.HTTP_200_OK)
                    
                    if is_scheduled == True:
                        post.is_posted = False
                        post.save()
                        # notify = send_notification(f'Post scheduled for {scheduled_at} Successfully', 'Flowkar', 
                        #                         [post.user.onesignal_player])
                        # print(notify)
                        return Response({'status': True, 'message': 'Post scheduled successfully', 'data': response_data}, status=status.HTTP_200_OK)
                    
                    if is_approved == False:
                        post.is_posted = False
                        post.is_approved = False
                        post.save()
                        notify = send_notification(f'Post In For Approval', 'Flowkar', 
                                                [post.user.onesignal_player])
                        print(notify)
                        return Response({'status':True,"message":'Post Submitted For Approval'},status=status.HTTP_200_OK)


                    third_party_data = ThirdPartyAuth.objects.get(brand_id=brand_id)
                    all_files = response_data['files']
                    original_files = [
                        'media/' + file.split('/media/')[-1] for file in all_files]
                    statuses = {}

                    if check_sub['limits']['posts_left'] == False :
                        if  instagram or facebook or linkedin or pinterest or vimeo or youtube or dailymotion or twitter or tumblr or reddit:
                            return Response({'status': False,'message': 'Please subscribe to a  Premuim plan'}, status=status.HTTP_400_BAD_REQUEST)
                        else:
                            return Response({'status': True, 'message': 'Post uploaded successfully', 'data': response_data, 'third_party': statuses}, status=status.HTTP_200_OK)

                    if instagram and third_party_data.instagram_check:
                        try:
                            all_images = []
                            all_video = []
                            all_mix_files = []
                            upload = False
                            for insta_files in all_files:
                                file_format = insta_files.split(
                                    ".")[-1].split('.')[-1]
                                print(file_format)
                                if file_format.lower() in image_formats:
                                    all_images.append(insta_files)
                                elif file_format.lower() in video_formats:
                                    all_video.append(insta_files)
                            
                            ## Instagram Crousel 
                            # if all_images and all_video:
                            #     all_mix_files = all_images + all_video
                            #     all_images = []
                            #     all_video = []

                            # print(all_images)
                            # print(all_video)
                            # if all_mix_files:
                            #     media_urls = all_mix_files
                            #     print('media_urls',media_urls)
                            #     instagram_upload_id = create_carousel_post(
                            #         third_party_data.insta_user_id,
                            #         third_party_data.insta_auth_token,
                            #         media_urls,
                            #         description
                            #     )
                            #     if instagram_upload_id:
                            #         post.instagram_id = instagram_upload_id
                            #         upload = True
                            

                            if len(all_video) > 0:
                                print(all_video)
                                upload, instagram_upload_id_video = upload_and_publish_instagram_video(
                                    third_party_data.insta_user_id, third_party_data.insta_auth_token, all_video[0], description)
                                if upload == True:
                                    post.instagram_id = instagram_upload_id_video

                            if len(all_images) > 0:
                                print(all_images)
                                upload, instagram_upload_id = upload_images_to_instagram(
                                    third_party_data.insta_user_id, third_party_data.insta_auth_token, all_images, description)
                                if upload == True:
                                    post.instagram_id = instagram_upload_id
                            if upload == True:
                                statuses['Instagram'] = True
                            else:
                                post.instagram = False
                                statuses['Instagram'] = False
                            post.save()
                        except TypeError:
                            statuses['Instagram'] = False

                    if twitter and third_party_data.thread_check:
                        try:
                            all_images = []
                            all_video = []
                            upload = False
                            for thread_files in all_files:
                                file_format = thread_files.split(
                                    ".")[-1].split('.')[-1]
                                print(file_format)
                                if file_format.lower() in image_formats:
                                    all_images.append(thread_files)
                                else:
                                    all_video.append(thread_files)
                            if len(all_video) > 0:
                                print(all_video)
                                upload, thread_upload_id_video = upload_and_publish_thread_video(
                                    third_party_data.thread_user_id, third_party_data.thread_auth_token, all_video[0], description)
                                if upload == True:
                                    post.twitter_id = thread_upload_id_video

                            if len(all_images) > 0:
                                print(all_images)
                                upload, thread_upload_id = upload_images_to_thread(
                                    third_party_data.thread_user_id, third_party_data.thread_auth_token, all_images, description)
                                if upload == True:
                                    post.twitter_id = thread_upload_id
                            if upload == True:
                                statuses['Thread'] = True
                            else:
                                post.twitter = False
                                statuses['Thread'] = False
                            post.save()
                        except TypeError:
                            statuses['Thread'] = False

                    if x and third_party_data.x_check:
                        access_token = get_and_update_x_token(third_party_data.x_refresh_token, brand_id)
                        print(access_token)
                        try:
                            all_images = []
                            all_video = []
                            upload = False
                            for x_files in original_files:
                                file_format = x_files.split(
                                    ".")[-1].split('.')[-1]
                                print(file_format)
                                if file_format.lower() in image_formats:
                                    all_images.append(x_files)
                                else:
                                    all_video.append(x_files)
                            if len(all_video) > 0:
                                upload, x_upload_id_video = upload_video_and_post_tweet(
                                    access_token, all_video, title)
                                if upload == True:
                                    post.x_id = x_upload_id_video
                                    post.x = True
                                    post.save()
                            if len(all_images) > 0:
                                upload, x_upload_id_image = post_tweet_with_asset(
                                    access_token, all_images, title)
                                if upload == True:
                                    post.x_id = x_upload_id_image
                                    post.x = True
                                    post.save()
                            if upload == True:
                                statuses['X'] = True
                        except TypeError:
                            statuses['X'] = False

                    if mastadon and third_party_data.mastodon_check:
                        if len(description) > 500:
                            description = description[:500]
                        try:
                            all_images = []
                            all_video = []
                            upload = False
                            for mastodon_files in original_files:
                                file_format = mastodon_files.split(
                                    ".")[-1].split('.')[-1]
                                print(file_format)
                                if file_format.lower() in image_formats:
                                    all_images.append(mastodon_files)
                                else:
                                    all_video.append(mastodon_files)

                            print("all_video",all_video)
                            print("all_images",all_images)
                            if len(all_video) > 0:
                                upload, mastodon_upload_id_video = post_mastodon_video_status(
                                    'mastodon.social',third_party_data.mastodon_token,description,all_video)
                                if upload == True:
                                    post.mastadon_id = mastodon_upload_id_video
                                    post.mastadon = True
                                    post.save()
                                    
                            if len(all_images) > 0:
                                upload, mastodon_upload_id_image = post_mastodon_status_with_images(
                                    'mastodon.social',third_party_data.mastodon_token,description,all_images)
                                if upload == True:
                                    post.mastadon_id = mastodon_upload_id_image
                                    post.mastadon = True
                                    post.save()
                            if upload == True:
                                statuses['Mastodon'] = True
                            else:
                                post.mastadon = False
                                statuses['Mastodon'] = False
                            post.save()

                        except (TypeError,Exception):
                            statuses['Mastodon'] = False

                    if dailymotion and third_party_data.tiktok_check:
                        try:
                            all_images = []
                            all_video = []
                            upload = False
                            for tiktok in all_files:
                                if tiktok.startswith("http://"):
                                    tiktok = tiktok.replace(
                                        "http://", "https://", 1)
                                file_format = tiktok.split(".")[-1].split('.')[-1]
                                print(file_format)
                                if file_format.lower() in image_formats:
                                    all_images.append(tiktok)
                                else:
                                    all_video.append(tiktok)
                            if len(all_video) > 0:
                                upload, tiktok_upload_id_video = tiktok_upload_videos(
                                    third_party_data.tiktok_access_token, all_video, description)
                                if upload == True:
                                    post.tiktok_id = tiktok_upload_id_video
                            if upload == True:
                                statuses['TikTok'] = True
                            else:
                                post.tiktok = False
                                statuses['TikTok'] = False
                        except TypeError:
                            statuses['TikTok'] = False
                        post.save()

                    if facebook and third_party_data.facebook_check:
                        try:
                            all_images = []
                            all_video = []
                            upload = False
                            for facebook_files in all_files:
                                file_format = facebook_files.split(
                                    ".")[-1].split('.')[-1]
                                print(file_format)
                                if file_format.lower() in image_formats:
                                    all_images.append(facebook_files)
                                else:
                                    all_video.append(facebook_files)
                            if len(all_video) > 0:
                                upload, facebook_upload_id_video = upload_facebook_videos(
                                    third_party_data.facebook_page_id, third_party_data.facebook_token, all_video[0], description)
                                if upload == True:
                                    post.facebook_id = facebook_upload_id_video

                            if len(all_images) > 0:
                                upload, facebook_upload_id = upload_facebook_images(
                                    third_party_data.facebook_page_id, third_party_data.facebook_token, all_images, description)
                                if upload == True:
                                    post.facebook_id = facebook_upload_id
                            if upload == True:
                                statuses['Facebook'] = True
                            else:
                                post.facebook = False
                                statuses['Facebook'] = False
                            post.save()
                        except TypeError:
                            statuses['Facebook'] = False

                    if linkedin and third_party_data.linkedin_check:
                        video_array = []
                        image_array = []
                        for data in original_files:
                            file_format = data.split(".")[-1].split('.')[-1]
                            if file_format.lower() in video_formats:
                                video_array.append(data)
                            elif file_format.lower() in image_formats:
                                image_array.append(data)
                        if len(video_array) > 0:
                            for upload_linkedin in video_array:
                                media_types = [
                                    f'video/{upload_linkedin.split(".")[-1]}']
                                print(media_types)
                                print(upload_linkedin)
                                upload_linkedin_video, uploaded_linkedin_id = create_linkedin_post_with_video(
                                    third_party_data.linked_in_token, third_party_data.linkedin_creds, description, [upload_linkedin], media_types)
                                if upload_linkedin_video == True:
                                    post.linkedin_id = uploaded_linkedin_id
                                    statuses['LinkedIn'] = True
                                else:
                                    post.linkedin = False
                                    statuses['LinkedIn'] = False
                        if len(image_array) > 0:
                            media_types = [
                                f'image/{file.split(".")[-1]}' for file in image_array]
                            upload_linkedin_photo, uploaded_linkedin_id = create_linkedin_post_with_multiple_media(
                                third_party_data.linked_in_token, third_party_data.linkedin_creds, description, original_files, media_types)
                            if upload_linkedin_photo == True:
                                post.linkedin_id = uploaded_linkedin_id
                                statuses['LinkedIn'] = True
                            else:
                                post.linkedin = False
                                statuses['LinkedIn'] = False
                        post.save()
                    if pinterest and third_party_data.pinterest_check:
                        pin_image_array = []
                        for pin_data in all_files:
                            file_format = pin_data.split(".")[-1].split('.')[-1]
                            print(file_format)
                            if file_format.lower() in image_formats:
                                pin_image_array.append(pin_data)
                        for upload_pin in pin_image_array:
                            if title == "":
                                title = "Flowkar"
                            if description == "":
                                description = "Flowkar"
                            pin_post, pin_id = post_pin(
                                third_party_data.pinterest_creds, upload_pin, title, description)
                            if pin_post == True:
                                post.pinterest_id = pin_id
                                statuses['Pinterest'] = True
                            else:
                                statuses['Pinterest'] = False
                        else:
                            pass
                    post.save()
                    if tumblr and third_party_data.tumblr_check:
                        all_images = []
                        all_video = []
                        for file in original_files:
                            file_format = file.split(".")[-1]
                            if file_format.lower() in video_formats:
                                all_video.append(file)
                            elif file_format.lower() in image_formats:
                                all_images.append(file)
                        print(f'Image Array = {all_images}')
                        if len(all_images) > 0:
                            upload_tumblr, check_tumblr = upload_tumblr_photo(
                                third_party_data.tumbler_token, third_party_data.tumbler_secret, all_images, post.description)
                            if check_tumblr == True:
                                post.tumblr_id = upload_tumblr
                                statuses['Tumbler'] = True
                            else:
                                post.tumblr = False
                                statuses['Tumbler'] = False
                        if len(all_video) > 0:
                            for fi in all_video:
                                upload_tumblr, check_tumblr = upload_tumblr_video(
                                    third_party_data.tumbler_token, third_party_data.tumbler_secret, fi, post.description)
                                if check_tumblr == True:
                                    post.tumblr_id = upload_tumblr
                                    statuses['Tumbler'] = True
                                else:
                                    post.tumblr = False
                                    statuses['Tumbler'] = False
                    else:
                        pass
                    post.save()
                    if vimeo and third_party_data.vimeo_check:
                        vimeo_array = []
                        for vimeo_data in original_files:
                            file_format = vimeo_data.split(".")[-1].split('.')[-1]
                            if file_format.lower() in video_formats:
                                vimeo_array.append(vimeo_data)
                        for upload_vimeo in vimeo_array:
                            vimeo_upload, vimeo_id = upload_vimeo_video(
                                third_party_data.vimeo_creds, upload_vimeo, title, description)
                            if vimeo_upload == True:
                                post.vimeo_id = vimeo_id
                                statuses['Vimeo'] = True
                            else:
                                post.vimeo = False
                                statuses['Vimeo'] = False
                        post.save()

                    if youtube and third_party_data.youtube_check:
                        y_array = []

                        for y_data in original_files:
                            file_format = y_data.split(".")[-1].split('.')[-1]
                            if file_format.lower() in video_formats:
                                y_array.append(y_data)

                        for upload_y in y_array:
                            pload, youtube_id = upload_video(
                                creds=f'youtube/{brand_id}', file=upload_y, title=title, description=description)
                            if pload == True:
                                post.youtube_id = youtube_id
                                statuses['Youtube'] = True
                            else:
                                post.youtube = False
                                statuses['Youtube'] = False
                        post.save()

                    if reddit and third_party_data.reddit_check:
                        check_reddit = True
                        print(original_files)
                        upload_reddit, check_reddit = upload_photo_to_reddit(
                            third_party_data.reddit_token, post.description, f'{header}{domain}/{original_files[0]}')
                        if check_reddit == True:
                            statuses['Reddit'] = True
                        else:
                            post.reddit = False
                            statuses['Reddit'] = False
                    else:
                        pass
                    post.save()
                    error_message = ''
                    try:
                        failed_platforms = [platform for platform, status in statuses.items(
                        ) if not status and platform != 'message']
                        if failed_platforms:
                            error_message = f"{', '.join(failed_platforms)} post upload failed"
                    except KeyError:
                        pass
                    statuses['message'] = error_message

                # notify = send_notification('Post uploaded Successfully', 'Flowkar', 
                #                            [post.user.onesignal_player])

                social_validated_data = {
                    'facebook': facebook,
                    'instagram': instagram,
                    'linkedin': linkedin,
                    'pinterest': pinterest,
                    'vimeo': vimeo,
                    'youtube': youtube,
                    'tiktok': dailymotion,
                    'twitter': twitter,
                    'tumblr': False,
                    'reddit': False
                }
                is_any_post_social = check_platform_posts_last_24h(brand_id,social_validated_data)
                social_referal_points = db_get_points("social_post_upload")
                for platform, is_allowed in is_any_post_social.items():
                        if is_allowed:
                            db_update_points(user_id, social_referal_points[0],"Social Post Upload Reward Credited")
                    
                
                print("is_any_post",is_any_post)
                if is_any_post == False:
                    referal_points = db_get_points("upload_post")
                    db_update_points(user_id,referal_points[0],"Post Upload Reward Credited")

                if is_text_post:
                    response_data = PostSerializer(
                        post, context={'request': request}).data
                    statuses = {}
                return Response({'status': True, 'message': 'Post uploaded successfully', 'data': response_data, 'third_party': statuses}, status=status.HTTP_200_OK)
            else:
                return Response({'status': False, 'message': 'Invalid Data', 'data': serializer.errors}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class UpdatePostStatusView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            brand_id = request.headers.get('brand')
            post_id = request.data.get('post_id')

            if not post_id:
                return Response({'status': False, 'message': 'Post ID is required'}, status=status.HTTP_400_BAD_REQUEST)
            try:
                post = Post.objects.get(id=post_id, user_id=user_id, brand_id=brand_id)
            except Post.DoesNotExist:
                return Response({'status': False, 'message': 'Post not found.'},status=status.HTTP_400_BAD_REQUEST)
            post.is_posted = True
            post.save()
            return Response({'status': True,'message': 'Post Upload successfully','data': {'post_id': post.id,'is_posted': post.is_posted}},status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)



class DraftPostsView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    pagination_class = CustomPagination  

    def get(self, request):
        try:
            domain = request.get_host()
            if '127.0.0.1' in  domain:
                header = 'https://'
            else:
                header = 'https://'
        except ValueError:
                header = ''
                domain = ''
        try:
            auth_token = request.headers.get('Authorization')
            brand_id = request.headers.get('brand')
            user_id = decode_token(auth_token)

            user = UserRegistration.objects.get(pk=user_id)
            user_name = user.name
            username = user.username
            profile_picture = ''
            if user.profile_picture:
                profile_picture = f"{header}{domain}{user.profile_picture.url}"

            draft_posts = Post.objects.filter(
                user_id=user_id, 
                brand_id=brand_id, 
                is_posted=False, 
                is_deleted=False,
                is_scheduled=False
            ).select_related(
                'user',
                'brand'
            ).only(
                'id', 'user_id', 'brand_id', 'created_at', 'is_posted', 
                'is_deleted', 'is_scheduled'
            ).order_by('-created_at')

            paginator = self.pagination_class()
            result_page = paginator.paginate_queryset(draft_posts, request)
            serialized_posts = PostSerializer(
                result_page,
                many=True,
                context={'request': request}).data

            return paginator.get_paginated_response({
                'status': True,
                'message': 'Draft posts retrieved successfully',
                'name': user_name,
                'username': username,
                'profile_picture': profile_picture,
                'data': serialized_posts
            })

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        


class DeleteDraftView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def get(self, request, id):
        try:
            auth_token = request.headers.get('Authorization')
            brand_id = request.headers.get('brand')
            user_id = decode_token(auth_token)
            try:
                post = Post.objects.get(pk=id, user_id=user_id, brand_id=brand_id)
            except Post.DoesNotExist:
                return Response({'status': False, 'message': 'Post not found.'},status=status.HTTP_400_BAD_REQUEST)
            post.delete()
            return Response({'status': True,'message': 'Post deleted successfully','deleted_post_id': int(id) },status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error: {str(e)}'},status=status.HTTP_400_BAD_REQUEST)



class UpdatePostView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            post_id = request.data.get('post_id')
            new_title = request.data.get('title')
            tagged_in = request.data.get('tagged_in')
            new_description = request.data.get('description')
            hashtags = request.data.get('hashtags')
            try:
                post = Post.objects.get(id=post_id, user_id=user_id)
            except Post.DoesNotExist:
                return Response({'status': False, 'message': 'Post not found or you do not have permission to edit it'}, status=status.HTTP_400_BAD_REQUEST)

            if new_title is not None:
                post.title = new_title
            if new_description is not None:
                post.description = new_description
            if hashtags is not None:
                hashtags = json.loads(hashtags)
                for hashtag in hashtags:
                    try:
                        is_present = HashTags.objects.get(name=hashtag)
                        is_present.posts.append(post.pk)
                        is_present.save()
                    except HashTags.DoesNotExist:
                        create = HashTags.objects.create(
                            name=hashtag, posts=[post.pk])
            if tagged_in is not None:
                for tags in tagged_in:
                    tagger = UserRegistration.objects.get(pk=user_id)
                    tagged_user = UserRegistration.objects.get(pk=tags)
                    onesignal_id = tagged_user.onesignal_player
                    if tagger == tagged_user:
                        pass
                    else:
                        Notification.objects.create(
                            user_id=tagged_user.pk,
                            type='tag',
                            from_user_id=user_id,
                            post_id=post.pk,
                            title='Flowkar',
                            message=f'You were tagged in a post by {tagger.name}'
                        )
                        send_notification(
                            f'You were tagged in a post by {tagger.name}', 'Flowkar', [onesignal_id])
                        print('Notification Sent')
                post.tagged_in = tagged_in
            post.save()

            return Response({'status': True, 'message': 'Post updated successfully', 'data': {'id': post.pk, 'title': new_title, 'description': new_description}}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)



class GetSinglePostView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    pagination_class = CustomPagination

    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            post_id = request.query_params.get('post_id')
            if not post_id:
                return Response({'status': False, 'message': 'post_id is required'}, status=status.HTTP_400_BAD_REQUEST)
            data = Post.objects.get(pk=post_id)
            third_party_obj = ThirdPartyAuth.objects.get(brand_id=data.brand.pk)
            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''
            paginator = self.pagination_class()
            all_data = []
            id = data.pk
            title = data.title
            des = data.description
            location = data.location
            likes = LikePost.objects.filter(post_id=data.pk).count()
            dislikes = data.dislikes
            comment_count = data.comments_count
            tagged = data.tagged_in
            created_at = data.created_at
            files = [f'{header}{domain}{file.file.url}' for file in PostFiles.objects.filter(
                Q(post_id=data.pk))]
            if files:
                width, height = get_image_dimensions(files[0])
            else:
                width, height = 0, 0
            post_user_id = data.user.pk
            username = data.user.username
            name = data.user.name
            profile_image = f'{header}{domain}{data.user.profile_picture.url}' if data.user.profile_picture else ''
            is_liked = False
            latest_comment = ''
            try:
                comment = Comment.objects.filter(
                    post=data).order_by('-pk').first()
                if comment is not None:
                    latest_comment = f'{comment.user.username} {comment.comment_text}'
                else:
                    latest_comment = ''

            except Comment.DoesNotExist:
                latest_comment = ''
            try:
                LikePost.objects.get(
                    Q(post_id=data.pk), Q(user_id=user_id))
                is_liked = True
            except:
                is_liked = False
            try:
                SavedPost.objects.get(
                    Q(post_id=data.pk), Q(user_id=user_id))
                is_saved = True
            except:
                is_saved = False
            thumbail_files = []
            post_files = PostFiles.objects.filter(Q(post=data), Q(
                is_video=True)).prefetch_related('postfilesthumbnail_set')
            for file in post_files:
                thumbnails = file.postfilesthumbnail_set.all()
                thumbail_files.extend(
                    [f'{header}{domain}{thumbnail.file.url}' for thumbnail in thumbnails])
            
            social_links = []
            if data.instagram and data.instagram_id:
                instagram_url_status , instagram_url = get_post_share_url(third_party_obj.insta_auth_token,data.instagram_id)
                if instagram_url_status:
                    append_obj = {
                        "Instagram":True,
                        "link":instagram_url
                    }
                    social_links.append(append_obj)
                else:
                    append_obj = {
                        "Instagram":False,
                        "link":instagram_url
                    }
                    social_links.append(append_obj)

            if data.facebook and data.facebook_id:
                facebook_url_status , facebook_url = get_facebook_share_url(third_party_obj.facebook_token,data.facebook_id)
                if facebook_url_status:
                    append_obj = {
                        "Facebook":True,
                        "link":facebook_url
                    }
                    social_links.append(append_obj)
                else:
                    append_obj = {
                        "Facebook":False,
                        "link":facebook_url
                    }
                    social_links.append(append_obj)

            if data.twitter and data.twitter_id:
                threads_url_status , threads_url = get_post_share_threads_url(third_party_obj.thread_auth_token,data.twitter_id)
                if threads_url_status:
                    append_obj = {
                        "Threads":True,
                        "link":threads_url
                    }
                    social_links.append(append_obj)
                else:
                    append_obj = {
                        "Threads":False,
                        "link":threads_url
                    }
                    social_links.append(append_obj)

            if data.youtube and data.youtube_id:
                    append_obj = {
                        "Youtube":True,
                        "link":f"https://www.youtube.com/watch?v={data.youtube_id}"
                    }
                    social_links.append(append_obj)

            if data.vimeo and data.vimeo_id:
                    vimeo_post_id = extract_vimeo_id(data.vimeo_id)
                    append_obj = {
                        "Vimeo":True,
                        "link":f"https://vimeo.com/{vimeo_post_id}"
                    }
                    social_links.append(append_obj)

            if data.linkedin and data.linkedin_id:
                    append_obj = {
                        "Linkedin":True,
                        "link":f"https://www.linkedin.com/feed/update/{data.linkedin_id}"
                    }
                    social_links.append(append_obj)


            

            append_object = {
                'id': id,
                'title': title,
                'description': des,
                'location': location,
                'likes': likes,
                'dislikes': dislikes,
                'comments_count': comment_count,
                'tagged_in': tagged,
                'created_at': created_at,
                'files': files,
                'width': width,
                'height': height,
                'thumbail_files': thumbail_files,
                'latest_comment': latest_comment,
                'social_links':social_links,
                'user': {
                    'user_id': post_user_id,
                    'username': username,
                    'name': name,
                    'profile_image': profile_image
                },
                'is_liked': is_liked,
                'is_saved': is_saved,
            }
            all_data.append(append_object)
            result_page = paginator.paginate_queryset(all_data, request)
            return paginator.get_paginated_response({'status': True, 'message': 'Data Found Successfully', 'data': result_page})
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        
         

class GetSinglePostWebView(APIView):
    pagination_class = CustomPagination

    @sentry_sdk.trace
    def get(self, request):
        try:
            post_id = decrypt_data(request.query_params.get('post_id'))
            if not post_id:
                return Response({'status': False, 'message': 'post_id is required'}, status=status.HTTP_400_BAD_REQUEST)

            data = Post.objects.get(pk=post_id)
            third_party_obj = ThirdPartyAuth.objects.get(brand_id=data.brand.pk)
            try:
                domain = request.get_host()
                header = 'https://'
            except ValueError:
                header = ''
                domain = ''

            paginator = self.pagination_class()
            all_data = []

            files = [f'{header}{domain}{file.file.url}' for file in PostFiles.objects.filter(post_id=data.pk)]
            width, height = get_image_dimensions(files[0]) if files else (0, 0)

            try:
                comment = Comment.objects.filter(post=data).order_by('-pk').first()
                latest_comment = f'{comment.user.username} {comment.comment_text}' if comment else ''
            except Comment.DoesNotExist:
                latest_comment = ''

            is_liked = LikePost.objects.filter(post_id=data.pk).exists()
            is_saved = SavedPost.objects.filter(post_id=data.pk).exists()

            thumbail_files = []
            post_files = PostFiles.objects.filter(post=data, is_video=True).prefetch_related('postfilesthumbnail_set')
            for file in post_files:
                thumbnails = file.postfilesthumbnail_set.all()
                thumbail_files.extend([f'{header}{domain}{thumb.file.url}' for thumb in thumbnails])

            comments = Comment.objects.filter(post=data).order_by('-created_at')
            comment_list = []
            for comment in comments:
                try:
                    is_liked_comment = LikeComment.objects.filter(user_id=data.user.pk, comment=comment).exists()
                except:
                    is_liked_comment = False

                comment_obj = {
                    'id': comment.id,
                    'comment_text': comment.comment_text,
                    'created_at': comment.created_at,
                    'user': {
                        'id': comment.user.id,
                        'username': comment.user.username,
                        'profile_image': f"{header}{domain}{comment.user.profile_picture.url}" if comment.user.profile_picture else None
                    },
                    'likes_count': LikeComment.objects.filter(comment=comment).count(),
                    'replies': [],  
                    'is_liked': is_liked_comment
                }
                comment_list.append(comment_obj)
            social_links = []
            if data.instagram and data.instagram_id:
                instagram_url_status , instagram_url = get_post_share_url(third_party_obj.insta_auth_token,data.instagram_id)
                if instagram_url_status:
                    append_obj = {
                        "Instagram":True,
                        "link":instagram_url
                    }
                    social_links.append(append_obj)
                else:
                    append_obj = {
                        "Instagram":False,
                        "link":instagram_url
                    }
                    social_links.append(append_obj)

            if data.facebook and data.facebook_id:
                facebook_url_status , facebook_url = get_facebook_share_url(third_party_obj.facebook_token,data.facebook_id)
                if facebook_url_status:
                    append_obj = {
                        "Facebook":True,
                        "link":facebook_url
                    }
                    social_links.append(append_obj)
                else:
                    append_obj = {
                        "Facebook":False,
                        "link":facebook_url
                    }
                    social_links.append(append_obj)

            if data.twitter and data.twitter_id:
                threads_url_status , threads_url = get_post_share_threads_url(third_party_obj.thread_auth_token,data.twitter_id)
                if threads_url_status:
                    append_obj = {
                        "Threads":True,
                        "link":threads_url
                    }
                    social_links.append(append_obj)
                else:
                    append_obj = {
                        "Threads":False,
                        "link":threads_url
                    }
                    social_links.append(append_obj)

            if data.youtube and data.youtube_id:
                    append_obj = {
                        "Youtube":True,
                        "link":f"https://www.youtube.com/watch?v={data.youtube_id}"
                    }
                    social_links.append(append_obj)

            if data.vimeo and data.vimeo_id:
                    vimeo_post_id = extract_vimeo_id(data.vimeo_id)
                    append_obj = {
                        "Vimeo":True,
                        "link":f"https://vimeo.com/{vimeo_post_id}"
                    }
                    social_links.append(append_obj)

            if data.linkedin and data.linkedin_id:
                    append_obj = {
                        "Linkedin":True,
                        "link":f"https://www.linkedin.com/feed/update/{data.linkedin_id}"
                    }
                    social_links.append(append_obj)

            append_object = {
                'id': data.pk,
                'title': data.title,
                'description': data.description,
                'location': data.location,
                'likes': LikePost.objects.filter(post_id=data.pk).count(),
                'dislikes': data.dislikes,
                'comments_count': data.comments_count,
                'tagged_in': data.tagged_in,
                'created_at': data.created_at,
                'files': files,
                'width': width,
                'height': height,
                'thumbail_files': thumbail_files,
                'latest_comment': latest_comment,
                'comments': comment_list,
                'social_links':social_links,
                'user': {
                    'user_id': data.user.pk,
                    'username': data.user.username,
                    'name': data.user.name,
                    'profile_image': f'{header}{domain}{data.user.profile_picture.url}' if data.user.profile_picture else ''
                },
                'is_liked': is_liked,
                'is_saved': is_saved,
            }

            all_data.append(append_object)
            result_page = paginator.paginate_queryset(all_data, request)
            return paginator.get_paginated_response({'status': True, 'message': 'Data Found Successfully', 'data': result_page})

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class GetPostView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    pagination_class = CustomPagination

    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            request_type = request.query_params.get('request_type')

            # Use select_related and prefetch_related for efficient DB queries
            post_queryset = Post.objects.select_related('user').filter(
                is_deleted=False, 
                is_posted=True,
                is_private=False,
            )

            # Apply request_type filter
            if request_type == '1':
                post_queryset = post_queryset.filter(is_video=True,is_text_post=False).order_by('?')
            elif request_type == '2':
                post_queryset = post_queryset.filter(is_video=False).order_by('?')
            else:
                post_queryset = post_queryset.filter(is_text_post=False).order_by('?')

            # Efficient blocking and hiding filtering using a single DB query
            hidden_posts = HidePost.objects.filter(user_id=user_id).values_list('post_id', flat=True)
            blocked_users = Block.objects.filter(
                Q(from_user_id=user_id) | Q(to_user_id=user_id)
            ).values_list('from_user_id', 'to_user_id')
            
            # Create a set of all blocked user IDs
            blocked_user_ids = set()
            for from_id, to_id in blocked_users:
                if from_id == user_id:
                    blocked_user_ids.add(to_id)
                else:
                    blocked_user_ids.add(from_id)

            # Apply exclusion filters
            post_queryset = post_queryset.exclude(
                Q(id__in=hidden_posts) | 
                Q(user_id__in=blocked_user_ids)
            ).order_by('?')  # Changed from -created_at to ? for random ordering

            # Determine domain and header once
            try:
                domain = request.get_host()
                header = 'https://'
            except ValueError:
                header = ''
                domain = ''
                
            # Prefetch related data to reduce DB queries
            post_queryset = post_queryset.prefetch_related(
                'postfiles_set',
                'postfiles_set__postfilesthumbnail_set',
                Prefetch('likepost_set', queryset=LikePost.objects.filter(user_id=user_id), to_attr='user_likes'),
                Prefetch('savedpost_set', queryset=SavedPost.objects.filter(user_id=user_id), to_attr='user_saved')
            )

            # Get paginator for efficient slicing
            paginator = self.pagination_class()
            paginated_posts = paginator.paginate_queryset(post_queryset, request)
            
            # Process the paginated results using batch processing
            all_post_ids = [post.pk for post in paginated_posts]
            
            # Batch fetch likes counts
            likes_counts = dict(
                LikePost.objects.filter(post_id__in=all_post_ids)
                .values('post_id')
                .annotate(count=Count('post_id'))
                .values_list('post_id', 'count')
            )
            
            # Batch fetch latest comments
            latest_comments = {}
            comments = Comment.objects.filter(post_id__in=all_post_ids).select_related('user').order_by('post_id', '-pk')
            for comment in comments:
                if comment.post_id not in latest_comments:
                    latest_comments[comment.post_id] = f'{comment.user.username} {comment.comment_text}'
            
            # Process posts data
            result_data = []
            for post in paginated_posts:
                post_files = list(post.postfiles_set.all())
                files = [f'{header}{domain}{file.file.url}' for file in post_files]
                
                # Get image dimensions only if files exist
                width, height = 0, 0
                if files:
                    width, height = get_image_dimensions(files[0])
                
                # Get thumbnail files
                thumbnail_files = []
                for file in [f for f in post_files if f.is_video]:
                    thumbnails = file.postfilesthumbnail_set.all()
                    thumbnail_files.extend([f'{header}{domain}{thumbnail.file.url}' for thumbnail in thumbnails])
                
                # Check if post is liked or saved
                is_liked = len(post.user_likes) > 0
                is_saved = len(post.user_saved) > 0
                
                result_data.append({
                    'id': post.pk,
                    'title': post.title,
                    'description': post.description,
                    'location': post.location,
                    'likes': likes_counts.get(post.pk, 0),
                    'dislikes': post.dislikes,
                    'comments_count': post.comments_count,
                    'tagged_in': post.tagged_in,
                    'created_at': post.created_at,
                    'scheduled_at': post.scheduled_at if post.scheduled_at else '',
                    'files': files,
                    'width': width,
                    'height': height,
                    'thumbail_files': thumbnail_files,
                    'latest_comment': latest_comments.get(post.pk, ''),
                    'user': {
                        'user_id': post.user.pk,
                        'username': post.user.username,
                        'name': post.user.name,
                        'profile_image': f'{header}{domain}{post.user.profile_picture.url}' if post.user.profile_picture else ''
                    },
                    'is_liked': is_liked,
                    'is_saved': is_saved,
                    'is_text_post': post.is_text_post,
                })
            
            return paginator.get_paginated_response({
                'status': True, 
                'message': 'Data Found Successfully', 
                'data': result_data
            })

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


        

        
class GetReelsView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    pagination_class = ReelPagination

    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)

            try:
                domain = request.get_host()
                header = 'https://' if '127.0.0.1' in domain else 'https://'
            except ValueError:
                header = ''
                domain = ''

            # Get blocked/hidden/image posts
            hiden_post = set(HidePost.objects.filter(user_id=user_id).values_list('post_id', flat=True))
            image_posts = set(PostFiles.objects.filter(is_video=False).values_list('post_id', flat=True))
            blocked_users = set(Block.objects.filter(from_user_id=user_id).values_list('to_user_id', flat=True))
            from_blocked_users = set(Block.objects.filter(to_user_id=user_id).values_list('from_user_id', flat=True))

            # Prefetch only video files
            video_files_qs = PostFiles.objects.filter(is_video=True)
            posts = Post.objects.filter(
                is_deleted=False,
                is_posted=True,
                is_private=False
            ).exclude(
                id__in=image_posts
            ).exclude(
                id__in=hiden_post
            ).exclude(
                user_id__in=blocked_users
            ).exclude(
                user_id__in=from_blocked_users
            ).select_related('user').prefetch_related(
                Prefetch('postfiles_set', queryset=video_files_qs, to_attr='video_files')
            ).order_by('?')  # Changed from -created_at to ? for random ordering

            post_ids = list(posts.values_list('id', flat=True))

            # Likes count and user likes
            likes_count = dict(
                LikePost.objects.filter(post_id__in=post_ids)
                .values('post_id')
                .annotate(count=Count('id'))
                .values_list('post_id', 'count')
            )

            user_likes = set(
                LikePost.objects.filter(post_id__in=post_ids, user_id=user_id)
                .values_list('post_id', flat=True)
            )

            saved_posts = set(
                SavedPost.objects.filter(post_id__in=post_ids, user_id=user_id)
                .values_list('post_id', flat=True)
            )

            # Latest comments using subquery
            latest_comment_qs = Comment.objects.filter(post_id=OuterRef('pk')).order_by('-pk')
            posts = posts.annotate(
                latest_comment=Subquery(latest_comment_qs.values('comment_text')[:1])
            )

            all_data = []
            for post in posts:
                video_files = getattr(post, 'video_files', [])
                if not video_files:
                    continue

                files = [f'{header}{domain}{pf.file.url}' for pf in video_files]
                # width = video_files[0].width
                # height = video_files[0].height

                all_data.append({
                    'id': post.id,
                    'title': post.title,
                    'description': post.description,
                    'location': post.location,
                    'likes': likes_count.get(post.id, 0),
                    'dislikes': post.dislikes,
                    'comments_count': post.comments_count,
                    'tagged_in': post.tagged_in,
                    'created_at': post.created_at,
                    'files': files,
                    'latest_comment': post.latest_comment or '',
                    'user': {
                        'user_id': post.user.id,
                        'username': post.user.username,
                        'name': post.user.name,
                        'profile_image': f'{header}{domain}{post.user.profile_picture.url}' if post.user.profile_picture else ''
                    },
                    'is_liked': post.id in user_likes,
                    'is_saved': post.id in saved_posts,
                })

            paginator = self.pagination_class()
            result_page = paginator.paginate_queryset(all_data, request)

            return paginator.get_paginated_response({
                'status': True,
                'message': 'Data Found Successfully',
                'data': result_page
            })

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class GetUserReelsView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    pagination_class = CustomPagination

    @sentry_sdk.trace
    def get(self, request):
        try:
            try:
                domain = request.get_host()
                header = 'https://'  # Simplified this logic
            except ValueError:
                header = ''
                domain = ''

            auth_token = request.headers.get('Authorization')
            input_field = request.query_params.get('user_id')
            paginator = self.pagination_class()
            user_id = input_field if input_field else decode_token(auth_token)

            # Get all filter values in one go - avoid multiple queries
            hidden_post_ids = list(HidePost.objects.filter(user_id=user_id).values_list('post_id', flat=True))
            image_post_ids = list(PostFiles.objects.filter(is_video=False).values_list('post_id', flat=True))
            blocked_user_ids = list(Block.objects.filter(from_user_id=user_id).values_list('to_user_id', flat=True))
            from_blocked_user_ids = list(Block.objects.filter(to_user_id=user_id).values_list('from_user_id', flat=True))
            
            # Combined blocked users list
            all_blocked_users = set(blocked_user_ids + from_blocked_user_ids)

            # Main query with annotations and prefetching
            post_queryset = Post.objects.filter(
                user_id=user_id, 
                is_deleted=False, 
                is_posted=True,
                is_text_post=False
            ).exclude(
                Q(is_private=True) | 
                Q(id__in=image_post_ids) | 
                Q(id__in=hidden_post_ids) | 
                Q(user_id__in=all_blocked_users)
            ).select_related(
                'user'  # Get user data in the same query
            ).prefetch_related(
                Prefetch(
                    'postfiles_set',  
                    queryset=PostFiles.objects.all(),
                    to_attr='all_post_files'
                ),
                Prefetch(
                    'postfiles_set',
                    queryset=PostFiles.objects.filter(is_video=True).prefetch_related('postfilesthumbnail_set'),
                    to_attr='video_files'
                ),
                Prefetch(
                    'comment_set',  # Assuming related_name is 'comment_set'
                    queryset=Comment.objects.select_related('user').order_by('-pk')[:1],
                    to_attr='latest_comments'
                )
            ).annotate(
                likes_count=Count('likepost'),  # Count likes in the database
                is_liked=Exists(
                    LikePost.objects.filter(
                        post_id=OuterRef('pk'),
                        user_id=user_id
                    )
                ),
                is_saved=Exists(
                    SavedPost.objects.filter(
                        post_id=OuterRef('pk'),
                        user_id=user_id
                    )
                )
            ).order_by('-created_at')

            all_data = []
            for post in post_queryset:
                # Process files efficiently using prefetched data
                files = [f'{header}{domain}{file.file.url}' for file in post.all_post_files]
                width, height = get_image_dimensions(files[0]) if files else (0, 0)
                
                # Get latest comment efficiently
                latest_comment = ''
                if hasattr(post, 'latest_comments') and post.latest_comments:
                    comment = post.latest_comments[0]
                    latest_comment = f'{comment.user.username} {comment.comment_text}'
                
                # Process thumbnail files efficiently
                thumbnail_files = []
                for file in post.video_files:
                    thumbnails = file.postfilesthumbnail_set.all()
                    thumbnail_files.extend(
                        [f'{header}{domain}{thumbnail.file.url}' for thumbnail in thumbnails]
                    )
                
                append_object = {
                    'id': post.pk,
                    'title': post.title,
                    'description': post.description,
                    'location': post.location,
                    'likes': post.likes_count,
                    'dislikes': post.dislikes,
                    'comments_count': post.comments_count,
                    'tagged_in': post.tagged_in,
                    'created_at': post.created_at,
                    'files': files,
                    'width': width,
                    'height': height,
                    'thumbail_files': thumbnail_files,
                    'latest_comment': latest_comment,
                    'user': {
                        'user_id': post.user.pk,
                        'username': post.user.username,
                        'name': post.user.name,
                        'profile_image': f'{header}{domain}{post.user.profile_picture.url}' if post.user.profile_picture else ''
                    },
                    'is_liked': post.is_liked,
                    'is_saved': post.is_saved,
                    'facebook': post.facebook,
                    'instagram': post.instagram,
                    'linkedin': post.linkedin,
                    'pinterest': post.pinterest,
                    'vimeo': post.vimeo,
                    'youtube': post.youtube,
                    'dailymotion': post.dailymotion,
                    'reddit': post.reddit,
                    'tumblr': post.tumblr
                }
                all_data.append(append_object)

            result_page = paginator.paginate_queryset(all_data, request)
            return paginator.get_paginated_response({
                'status': True, 
                'message': 'Data Found Successfully', 
                'data': result_page
            })
            
        except Exception as e:
            return Response({'status': False, 'message': f'Error: {str(e)}'}, status=status.HTTP_400_BAD_REQUEST)


class StoryCreateView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            upload_files = request.FILES.getlist('upload_files')
            music = request.data.get('music')
            instagram = request.data.get('instagram')
            facebook = request.data.get('facebook')
            brand = request.headers.get('brand')
            title = request.data.get('title') 

            print(instagram, facebook, brand)
            try:
                domain = request.get_host()
                header = 'https://'  # Simplified this logic
            except ValueError:
                header = ''
            
            if upload_files is None:
                return Response({'status': False, 'message': 'upload_files field is required'}, status=status.HTTP_400_BAD_REQUEST)

            is_highlighted = True
            if title is None or title == '':
                is_highlighted = False

            create_story = Story.objects.create(
                user_id=user_id,
                music=music,
                is_highlighted=is_highlighted,
                title=title
            )

            for file in upload_files:
                story_file = ContentFile(file.read(), name=file.name)
                StoryFiles.objects.create(
                    story_id=create_story.pk, file=story_file)

            uploaded_files = StoryFiles.objects.filter(story_id=create_story.pk)
            media_urls = [f'{header}{domain}{file.file.url}' for file in uploaded_files]
            media_files = [f'.{file.file.url}' for file in uploaded_files]

            instagram_status = False
            facebook_status = False

            if brand:
                thirdparty_obj = ThirdPartyAuth.objects.get(brand_id=brand)
                if instagram == True or instagram == 'true' :
                    if thirdparty_obj.instagram_check:
                        instagram_status , instagram_response = upload_instagram_stories(thirdparty_obj.insta_user_id, thirdparty_obj.insta_auth_token, media_urls)
                if facebook == True or facebook == 'true' :
                    if thirdparty_obj.facebook_check:
                       facebook_status , facebook_response = upload_multiple_facebook_stories(thirdparty_obj.facebook_page_id, thirdparty_obj.facebook_token, media_files)

            return Response({'status': True, 'message': 'Story Uploaded Successfully',"thirdparty_obj":{'instagram':instagram_status,'facebook':facebook_status}}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status.HTTP_400_BAD_REQUEST)


class GetStoryHighlightingView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            user = UserRegistration.objects.get(id=user_id)
            stories = Story.objects.filter(
                user=user, is_deleted=False).exclude(is_highlighted=True)
            data = []
            for story in stories:
                file = StoryFiles.objects.get(story=story)
                stories_data = {
                    'id': story.id,
                    'username': user.username,
                    'profile_picture': user.profile_picture.url if user.profile_picture else '',
                    'name': user.name,
                    'files': file.file.url if file.file is not None else '',
                    'created_on': story.created_on,
                }
                data.append(stories_data)
            return Response({'status': True, 'data': data}, status=status.HTTP_200_OK)

        except UserRegistration.DoesNotExist:
            return Response({'status': False, 'message': 'User does not exist'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class StoryGetView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    pagination_class = StoryPagination

    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            query_user_id = request.query_params.get('user_id')
            story_id = request.query_params.get('story_id')
            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''
            if query_user_id or story_id:
                # Fetch stories based on user_id or story_id
                story_data = Story.objects.filter(
                    Q(user_id=query_user_id) | Q(pk=story_id)).order_by('-created_on')

                data = []
                for story in story_data:
                    print(
                        story.user.profile_picture.url if story.user.profile_picture else '123')
                    if story.user.id == user_id:
                        likes = LikeStory.objects.filter(story=story)
                        is_liked = False
                        try:
                            is_liked_by_user = LikeStory.objects.get(
                                story=story, user_id=user_id)
                            is_liked = True
                        except LikeStory.DoesNotExist:
                            is_liked = False
                        likes_data = [
                            {
                                'username': like.user.username,
                                'profile_image': f"{header}{domain}{like.user.profile_picture.url}" if like.user.profile_picture else None
                            } for like in likes
                        ]
                        views = StoryView.objects.filter(story=story).exclude(
                            user_id__in=[like.user.id for like in likes])
                        views_data = [
                            {
                                'username': view.user.username,
                                'profile_image': f"{header}{domain}{view.user.profile_picture.url}" if view.user.profile_picture else ''
                            } for view in views
                        ]
                        data.append({
                            'id': story.id,
                            'user_id': story.user.id,
                            'story_data': [
                                f'{header}{domain}{file.file.url}'
                                for file in StoryFiles.objects.filter(story_id=story.pk)
                                if file.file
                            ],
                            'username': story.user.name,
                            'profile_image': f"{header}{domain}{story.user.profile_picture.url}" if story.user.profile_picture else '',
                            'music': story.music,
                            'is_deleted': story.is_deleted,
                            'is_archieved': story.is_archieved,
                            'is_highlighted': story.is_highlighted,
                            'created_on': story.created_on,
                            'is_liked': is_liked,
                            'likes': likes_data,
                            'views': views_data,
                            'total_views': story.total_views
                        })
                    else:

                        if not StoryView.objects.filter(user_id=user_id, story=story):
                            StoryView.objects.create(
                                user_id=user_id, story=story)
                        is_liked = False
                        try:
                            is_liked_by_user = LikeStory.objects.get(
                                story=story, user_id=user_id)
                            is_liked = True
                        except LikeStory.DoesNotExist:
                            is_liked = False
                        data.append({
                            'id': story.id,
                            'user_id': story.user.id,
                            'story_data': [
                                f'{header}{domain}{file.file.url}'
                                for file in StoryFiles.objects.filter(story_id=story.pk)
                                if file.file
                            ],
                            'username': story.user.name,
                            'is_liked': is_liked,
                            'profile_image': f"{header}{domain}{story.user.profile_picture.url}" if story.user.profile_picture else '',
                            'music': story.music,
                            'is_deleted': story.is_deleted,
                            'is_archieved': story.is_archieved,
                            'is_highlighted': story.is_highlighted,
                            'created_on': story.created_on,
                        })

                paginator = self.pagination_class()
                result_page = paginator.paginate_queryset(data, request)
                return paginator.get_paginated_response({'status': True, 'message': 'Data Found Successfully', 'data': result_page})
            else:
                all_stories = Story.objects.filter(Q(is_deleted=False), Q(
                    is_highlighted=False), Q(is_archieved=False)).order_by('-created_on')

# Filter stories uploaded by the user and other stories
                user_stories = all_stories.filter(user_id=user_id)
                other_stories = all_stories.exclude(user_id=user_id)

                data = []
                added_user_ids = set()  # Set to track added user IDs

                # Add the user's own story first (if they have uploaded a story)
                if user_stories.exists():
                    for story in user_stories:
                        likes = LikeStory.objects.filter(story=story)
                        likes_data = [
                            {
                                'username': like.user.username,
                                'profile_image': f"{header}{domain}{like.user.profile_picture.url}" if like.user.profile_picture else None
                            } for like in likes
                        ]
                        views = StoryView.objects.filter(story=story).exclude(
                            user_id__in=[like.user.id for like in likes])
                        views_data = [
                            {
                                'username': view.user.username,
                                'profile_image': f"{header}{domain}{view.user.profile_picture.url}" if view.user.profile_picture else None
                            } for view in views
                        ]
                        append_data = {
                            'id': story.id,
                            'user_id': story.user.id,
                            'story_data': [
                                f'{header}{domain}{file.file.url}'
                                for file in StoryFiles.objects.filter(story_id=story.pk)
                                if file.file
                            ],
                            'username': story.user.name,
                            'profile_image': f"{header}{domain}{story.user.profile_picture.url}" if story.user.profile_picture else '',
                            'music': story.music,
                            'is_deleted': story.is_deleted,
                            'is_archieved': story.is_archieved,
                            'is_highlighted': story.is_highlighted,
                            'created_on': story.created_on,
                            'likes': likes_data,
                            'views': views_data,
                            'total_views': story.total_views,
                            'previous_user_id': None,  # No previous user as it's the first story
                            # Next user in other stories
                            'next_user_id': other_stories.first().user.id if other_stories.exists() else None
                        }
                        data.append(append_data)
                        added_user_ids.add(story.user.id)
                        break  # Add to set of added user IDs

                # Add other stories, ensuring no duplicate user_id
                for index, story in enumerate(other_stories):
                    if story.user.id in added_user_ids:
                        continue  # Skip if the user's story is already added

                    previous_user_id = user_id if index == 0 else other_stories[index - 1].user.id
                    next_user_id = other_stories[index + 1].user.id if index < len(
                        other_stories) - 1 else None

                    if not StoryView.objects.filter(user_id=user_id, story=story):
                        StoryView.objects.create(user_id=user_id, story=story)

                    append_data = {
                        'id': story.id,
                        'user_id': story.user.id,
                        'story_data': [
                            f'{header}{domain}{file.file.url}'
                            for file in StoryFiles.objects.filter(story_id=story.pk)
                            if file.file
                        ],
                        'username': story.user.name,
                        'profile_image': f"{header}{domain}{story.user.profile_picture.url}" if story.user.profile_picture else '',
                        'music': story.music,
                        'is_deleted': story.is_deleted,
                        'is_archieved': story.is_archieved,
                        'is_highlighted': story.is_highlighted,
                        'created_on': story.created_on,
                        'previous_user_id': previous_user_id,
                        'next_user_id': next_user_id
                    }
                    data.append(append_data)
                    added_user_ids.add(story.user.id)
                paginator = self.pagination_class()
                result_page = paginator.paginate_queryset(data, request)
                return paginator.get_paginated_response({'status': True, 'message': 'Data Found Successfully', 'data': result_page})
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class StoryGetAllView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    pagination_class = StoryPagination

    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            query_user_id = request.query_params.get('user_id')
            story_id = request.query_params.get('story_id')
            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''

            try:
                video_formats = [
                    'mp4', 'mkv', 'mov', 'avi', 'flv', 'wmv', 'webm',
                    'm4v', 'mpg', 'mpeg', '3gp', '3g2', 'mts', 'm2ts',
                    'ts', 'ogv', 'rm', 'rmvb'
                ]
                response_data = []
                # Optimize the main story query with select_related and prefetch_related
                time_threshold = now() - timedelta(hours=24)

                stories = Story.objects.filter(
                    is_deleted=False
                ).filter(
                    Q(created_on__gte=time_threshold) & Q(is_highlighted=False) | Q(created_on__gte=time_threshold) & Q(is_highlighted=True)
                ).select_related(
                    'user'
                ).prefetch_related(
                    'storyfiles_set',
                    'likestory_set'
                ).order_by('-created_on')


                # stories = Story.objects.filter(
                #     is_deleted=False
                # ).select_related(
                #     'user'
                # ).prefetch_related(
                #     'storyfiles_set',
                #     'likestory_set'
                # ).order_by('-created_on')

                # Get all story IDs for bulk like check
                story_ids = [story.id for story in stories]
                user_likes = set(LikeStory.objects.filter(
                    story_id__in=story_ids,
                    user_id=user_id
                ).values_list('story_id', flat=True))

                for story in stories:
                    username = story.user.name
                    userprofile = f'{header}{domain}{story.user.profile_picture.url}' if story.user.profile_picture else ''
                    
                    # Construct story_response with 'storytype' and 'storyfile'
                    story_response = []
                    for file in story.storyfiles_set.all():
                        is_liked = file.story.pk in user_likes
                        file_url = f'{header}{domain}{file.file.url}'
                        file_extension = file.file.url.split('.')[-1].lower()
                        storytype = 'video' if file_extension in video_formats else 'image'

                        story_response.append({
                            'story_id': file.story.pk,
                            'storytype': storytype,
                            'storyfile': file_url,
                            'is_liked': is_liked
                        })

                    # Check if the user already exists in response_data
                    user_exists = next(
                        (item for item in response_data if item['user_id'] == story.user.pk), None)

                    if user_exists:
                        # If user exists, append the new story object(s) to the stories key
                        user_exists['stories'].extend(story_response)
                    else:
                        # If user does not exist, create a new entry
                        append_object = {
                            'user_id': story.user.pk,
                            'username': username,
                            'userprofile': userprofile,
                            'stories': story_response
                        }
                        response_data.append(append_object)
                        
                return Response({'status': True, 'message': 'Data found successfully', 'data': response_data}, status=status.HTTP_200_OK)
            except Story.DoesNotExist:
                return Response({'status': False, 'message': 'Stories Not Found'})
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class HighlightCreateView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)

            title = request.data.get('title')
            story_data = request.data.get('story')

            if not story_data:
                return Response({'status': False, 'message': 'Story is required.'}, status=status.HTTP_400_BAD_REQUEST)

            highlight = Highlight.objects.create(
                user_id=user_id,
                title=title,
                story=story_data
            )

            return Response({
                'status': True,
                'message': 'Highlight created successfully.',
                'data': {
                    'id': highlight.id,
                    'title': highlight.title,
                    'story': highlight.story,
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class HighlightGetAllView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    pagination_class = StoryPagination

    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            query_user_id = request.query_params.get('user_id')

            target_user_id = query_user_id if query_user_id else user_id
            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''

            try:
                video_formats = [
                    'mp4', 'mkv', 'mov', 'avi', 'flv', 'wmv', 'webm',
                    'm4v', 'mpg', 'mpeg', '3gp', '3g2', 'mts', 'm2ts',
                    'ts', 'ogv', 'rm', 'rmvb'
                ]
                response_data = []
                story = Story.objects.filter(Q(
                    user_id=target_user_id), Q(is_highlighted=True),Q(is_archieved=False),Q(is_deleted=False))
                for data in story:
                    username = data.user.name
                    userprofile = f'{header}{domain}{data.user.profile_picture.url}' if data.user.profile_picture else ''
                    # Construct story_response with 'storytype' and 'storyfile'
                    story_response = []
                    for file in StoryFiles.objects.filter(story=data):
                        # Determine story type (example: assuming video or image based on file extension)
                        is_liked = False
                        try:
                            LikeStory.objects.get(
                                Q(story_id=file.story.pk), Q(user_id=user_id))
                            is_liked = True
                        except LikeStory.DoesNotExist:
                            is_liked = False
                        file_url = f'{header}{domain}{file.file.url}'
                        file_extension = file.file.url.split('.')[-1].lower()
                        # Adjust this logic based on your requirements
                        storytype = 'video' if file_extension in video_formats else 'image'

                        story_response.append({
                            'story_id': file.story.pk,
                            'title': file.story.title,
                            'storytype': storytype,
                            'storyfile': file_url,
                            'is_liked': is_liked
                        })

                    # Check if the user already exists in response_data
                        append_object = {
                            'user_id': data.user.pk,
                            'username': username,
                            'userprofile': userprofile,
                            'stories': story_response
                        }
                        response_data.append(append_object)
                        story_lenght = len(response_data)
                print(len(response_data))
                return Response({'status': True, 'message': 'Data found successfully', 'data': response_data[::-1]}, status=status.HTTP_200_OK)
            except Story.DoesNotExist:
                return Response({'status': False, 'message': 'Stories Not Found'})
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class HighlightListView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)

            highlights = Highlight.objects.filter(
                user_id=user_id, is_deleted=False).order_by('-created_at')

            data = []
            for highlight in highlights:
                data.append({
                    'id': highlight.id,
                    'title': highlight.title,
                    'story': highlight.story,
                    'created_at': highlight.created_at,
                })

            return Response({
                'status': True,
                'message': 'Highlights retrieved successfully.',
                'data': data
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

class HighlightUpdateView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            highlight_id = request.data.get('id')

            try:
                highlight = Highlight.objects.get(
                    id=highlight_id, user_id=user_id, is_deleted=False)
            except Highlight.DoesNotExist:
                return Response({'status': False, 'message': 'Highlight not found.'}, status=status.HTTP_400_BAD_REQUEST)

            title = request.data.get('title', highlight.title)
            story_data = request.data.get('story', highlight.story)

            highlight.title = title
            highlight.story = story_data
            highlight.save()

            return Response({
                'status': True,
                'message': 'Highlight updated successfully.',
                'data': {
                    'id': highlight.id,
                    'title': highlight.title,
                    'story': highlight.story,
                    'created_at': highlight.created_at
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class HighlightDeleteView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            highlight_id = request.query_params.get('id')

            try:
                story = Story.objects.get(pk=highlight_id, user_id=user_id, is_highlighted=True)
                story.is_deleted = True
                story.save()
                return Response({'status': True, 'message': 'Highlighted story deleted successfully.'}, status=status.HTTP_200_OK)
            except Story.DoesNotExist:
                return Response({'status': False, 'message': 'Highlighted story not found.'}, status=status.HTTP_404_NOT_FOUND)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class StoryDeleteView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            story_id = request.query_params.get('story_id')
            try:
                if story_id is not None:
                    story_data = Story.objects.get(pk=story_id)
                    print(story_data)
                    story_data.is_deleted = True
                    story_data.save()
                    return Response({'status': True, 'message': 'Story Deleted Successfully'}, status=status.HTTP_200_OK)
                return Response({'status': False, 'message': 'story_id is a required field'}, status=status.HTTP_400_BAD_REQUEST)
            except Story.DoesNotExist:
                return Response({'status': False, 'message': 'Story With Given Id Does Not Exists'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status.HTTP_400_BAD_REQUEST)


class CheckStoryView(APIView):
    @sentry_sdk.trace
    def get(self, request):
        try:
            story_data = Story.objects.filter(Q(is_deleted=False), Q(
                is_archieved=False), Q(is_highlighted=False))
            for data in story_data:
                print(data.created_on)
                upload_time = data.created_on
                is_24 = is_more_than_24_hours_old(str(upload_time))
                if is_24 == True:
                    data.is_deleted = True
                    data.save()
                else:
                    print('Story not old than 24 hours hence not removed')
                    pass
            return Response({'status': True})
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status.HTTP_400_BAD_REQUEST)


class LikeUnlikeStoryView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def post(self, request, id):
        auth_token = request.headers.get('Authorization')
        user_id = decode_token(auth_token)
        try:
            story = Story.objects.get(id=id)
            like, created = LikeStory.objects.get_or_create(
                story=story, user_id=user_id)
            if created:
                return Response({'status': True, 'message': 'Story liked successfully'}, status=status.HTTP_200_OK)
            else:
                like.delete()
                return Response({'status': True, 'message': 'Story unliked successfully'}, status=status.HTTP_200_OK)
        except Story.DoesNotExist:
            return Response({'status': False, 'message': 'Story not found'}, status=status.HTTP_400_BAD_REQUEST)
        except UserRegistration.DoesNotExist:
            return Response({'status': False, 'message': 'User not found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class DeletePostView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    def get(self, request):
        try:
            post_id = int(request.query_params.get('post_id'))
            try:
                post = Post.objects.get(pk=post_id)
                post.is_deleted = True
                post.save()
                hashtags = HashTags.objects.filter(posts__contains=post_id)
                for hashtag in hashtags:
                    hashtag.posts.remove(post_id)
                    hashtag.save()
                return Response({'status': True, 'message': 'Post Deleted Successfully'}, status=status.HTTP_200_OK)
            except Post.DoesNotExist:
                return Response({'status': False, 'message': 'Post Not Found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status.HTTP_400_BAD_REQUEST)


class FollowView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            to_user_id = request.data.get('to_user_id')

            if not to_user_id:
                return Response({'status': False, 'message': 'Invalid data'}, status=status.HTTP_400_BAD_REQUEST)
            try:
                to_user = UserRegistration.objects.get(id=to_user_id)
            except UserRegistration.DoesNotExist:
                return Response({'status': False, 'message': 'User not found'}, status=status.HTTP_400_BAD_REQUEST)
            if to_user_id == user_id:
                print(True)
                return Response({'status': False, 'message': 'Users cannot follow yourself'}, status=status.HTTP_400_BAD_REQUEST)
            follow, created = Follow.objects.get_or_create(
                from_user_id=user_id, to_user=to_user)
            if created:
                follow.is_accepted = not to_user.is_private
                follow.save()
                return Response({'status': True, 'message': 'Follow Successfull'}, status=status.HTTP_200_OK)
            else:
                return Response({'status': False, 'message': 'Already following'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class UnfollowView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            to_user_id = request.data.get('to_user_id')
            if not to_user_id:
                return Response({'status': False, 'message': 'Invalid data'}, status=status.HTTP_400_BAD_REQUEST)
            try:
                to_user = UserRegistration.objects.get(id=to_user_id)
            except UserRegistration.DoesNotExist:
                return Response({'status': False, 'message': 'User not found'}, status=status.HTTP_400_BAD_REQUEST)
            try:
                follow = Follow.objects.get(
                    from_user_id=user_id, to_user=to_user)
                follow.delete()
                return Response({'status': True, 'message': 'Unfollowed successfully'}, status=status.HTTP_200_OK)
            except Follow.DoesNotExist:
                return Response({'status': False, 'message': 'You Do Not Follow The User'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

class FollowListView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    pagination_class = UserProfilePagination

    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            auth_user_id = decode_token(auth_token)

            query_user_id = request.query_params.get('user_id')
            target_user_id = query_user_id if query_user_id else auth_user_id

            try:
                domain = request.get_host()
                header = 'https://' if '127.0.0.1' in domain else 'https://'
            except ValueError:
                header = ''
                domain = ''

            # # Get all followers (who follow target_user_id)
            # blocked_users = Block.objects.filter(from_user=target_user_id).values_list('to_user_id', flat=True)
            # followers_qs = Follow.objects.filter(to_user_id=target_user_id)
            # follower_ids = followers_qs.values_list('from_user', flat=True)
            # users = UserRegistration.objects.filter(id__in=follower_ids).exclude(id__in=blocked_users)

            # # Get authenticated user's following set for quick lookup
            # auth_following_ids = set(Follow.objects.filter(from_user_id=auth_user_id).values_list('to_user_id', flat=True))


            blocked_by_target = Block.objects.filter(from_user=target_user_id).values_list('to_user_id', flat=True)
            blocked_by_auth_user = Block.objects.filter(from_user=auth_user_id).values_list('to_user_id', flat=True)

            followers_qs = Follow.objects.filter(to_user_id=target_user_id)
            follower_ids = followers_qs.values_list('from_user', flat=True)
            users = UserRegistration.objects.filter(id__in=follower_ids, is_deleted=False).exclude(id__in=blocked_by_target).exclude(id__in=blocked_by_auth_user)

            auth_following_ids = set(Follow.objects.filter(from_user_id=auth_user_id).values_list('to_user_id', flat=True))


            data = []
            for follower in users:
                data.append({
                    'id': follower.id,
                    'username': follower.username,
                    'name': follower.name,
                    'profile_picture': f'{header}{domain}{follower.profile_picture.url}' if follower.profile_picture else None,
                    'is_following': follower.id in auth_following_ids
                })

            paginator = self.pagination_class()
            result_page = paginator.paginate_queryset(data, request)
            return paginator.get_paginated_response({'status': True, 'message': 'Data Found Successfully', 'data': result_page})

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        


class FollowingListView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    pagination_class = UserProfilePagination

    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            auth_user_id = decode_token(auth_token)
            query_user_id = request.query_params.get('user_id')
            target_user_id = query_user_id if query_user_id else auth_user_id

            try:
                domain = request.get_host()
                header = 'https://' if '127.0.0.1' in domain else 'https://'
            except ValueError:
                header = ''
                domain = ''

            

            blocked_by_target = Block.objects.filter(from_user=target_user_id).values_list('to_user_id', flat=True)
            # Exclude users blocked by authenticated user
            blocked_by_auth_user = Block.objects.filter(from_user=auth_user_id).values_list('to_user_id', flat=True)

            following_qs = Follow.objects.filter(from_user_id=target_user_id)
            following_user_ids = following_qs.values_list('to_user', flat=True)
            users = UserRegistration.objects.filter(id__in=following_user_ids, is_deleted=False).exclude(id__in=blocked_by_target).exclude(id__in=blocked_by_auth_user)

            # Get set of user_ids that authenticated user is following (for is_following flag)
            auth_following_ids = set(Follow.objects.filter(from_user_id=auth_user_id).values_list('to_user_id', flat=True))
            


            # # Get all users followed by target_user_id
            # blocked_users = Block.objects.filter(from_user=target_user_id).values_list('to_user_id', flat=True)
            # following_qs = Follow.objects.filter(from_user_id=target_user_id)
            # following_user_ids = following_qs.values_list('to_user', flat=True)
            # users = UserRegistration.objects.filter(id__in=following_user_ids).exclude(id__in=blocked_users)

            # # Get set of user_ids that authenticated user is following (for is_following flag)
            # auth_following_ids = set(Follow.objects.filter(from_user_id=auth_user_id).values_list('to_user_id', flat=True))


            data = []
            for user in users:
                data.append({
                    'id': user.id,
                    'username': user.username,
                    'name': user.name,
                    'profile_picture': f'{header}{domain}{user.profile_picture.url}' if user.profile_picture else None,
                    'is_following': user.id in auth_following_ids
                })

            paginator = self.pagination_class()
            result_page = paginator.paginate_queryset(data, request)
            return paginator.get_paginated_response({'status': True, 'message': 'Data Found Successfully', 'data': result_page})
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        


class LikePostView(APIView):

    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    def get(self, request):
        try:
            post_id = request.query_params.get('post_id')
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            try:
                post = Post.objects.get(pk=post_id)
                try:
                    get_like = LikePost.objects.get(
                        Q(user_id=user_id), Q(post_id=post_id))
                    post.likes -= 1
                    post.save()
                    get_like.delete()
                    return Response({'status': True, 'message': 'Post Disliked Successfully'}, status=status.HTTP_200_OK)
                except LikePost.DoesNotExist:
                    create_like = LikePost.objects.create(
                        post_id=post_id,
                        user_id=user_id
                    )
                    post.likes += 1
                    post.save()
                    return Response({'status': True, 'message': 'Post Liked Successfully'}, status=status.HTTP_200_OK)
            except Post.DoesNotExist:
                return Response({'status': False, 'message': 'Post Not Found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status.HTTP_400_BAD_REQUEST)


class FollowPendingInwordView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)

            follow_requests = Follow.objects.filter(
                Q(to_user=user_id), Q(is_accepted=False))
            follow_request_ids = follow_requests.values_list('from_user')
            follow = UserRegistration.objects.filter(id__in=follow_request_ids)
            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''
            data = [
                {
                    'id': follow_request.id,
                    'username': follow_request.username,
                    'profile_image': f'{header}{domain}{follow_request.profile_picture.url}'
                }
                for follow_request in follow
            ]
            return Response({'status': True, 'data': data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class FollowPendingOutwordView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)

            follow_requests = Follow.objects.filter(
                Q(from_user_id=user_id), Q(is_accepted=False))
            follow_request_ids = follow_requests.values_list('to_user')
            follow = UserRegistration.objects.filter(id__in=follow_request_ids)
            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''
            data = [
                {
                    'id': follow_request.id,
                    'username': follow_request.username,
                    'profile_image': f'{header}{domain}{follow_request.profile_picture.url}'
                }
                for follow_request in follow
            ]
            return Response({'status': True, 'data': data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class LikePostListView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    pagination_class = UserProfilePagination
    @sentry_sdk.trace
    def get(self, request):
        try:
            post_id = request.query_params.get('post_id')
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''
            liked_posts = LikePost.objects.filter(post_id=post_id)
            user_ids = liked_posts.values_list('user')
            follow = UserRegistration.objects.filter(id__in=user_ids, is_deleted=False)
            data = []
            for follower in follow:
                try:
                    follow = Follow.objects.get(
                        Q(to_user_id=follower.pk), Q(from_user_id=user_id))
                    is_following = True
                except Follow.DoesNotExist:
                    is_following = False
                data.append(
                    {
                        'id': follower.id,
                        'username': follower.username,
                        'name': follower.name,
                        'profile_picture': f'{header}{domain}{follower.profile_picture.url}' if follower.profile_picture else None,
                        'is_following': is_following
                    }

                )
            paginator = self.pagination_class()
            result_page = paginator.paginate_queryset(data, request)
            return paginator.get_paginated_response({'status': True, 'message': 'Data Found Successfully', 'data': result_page})
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status.HTTP_400_BAD_REQUEST)


class CommentView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def post(self, request, id):
        auth_token = request.headers.get('Authorization')
        user_id = decode_token(auth_token)
        comment_text = request.data.get('comment_text')
        if not comment_text:
            return Response({'status': False, 'message': 'Comment text is required'}, status=status.HTTP_400_BAD_REQUEST)
        try:
            post = Post.objects.get(id=id)
            comment = Comment.objects.create(
                post=post, user_id=user_id, comment_text=comment_text)
            post.comments_count += 1
            post.save()
            return Response({'status': True, 'message': 'Comment added successfully'}, status=status.HTTP_200_OK)
        except Post.DoesNotExist:
            return Response({'status': False, 'message': 'Post not found'}, status=status.HTTP_400_BAD_REQUEST)
        except UserRegistration.DoesNotExist:
            return Response({'status': False, 'message': 'User not found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

    @sentry_sdk.trace
    def get(self, request, id):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            post = Post.objects.get(id=id)
            comments = Comment.objects.filter(Q(post=post))
            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''
            data = []
            for comment in comments:
                try:
                    LikeComment.objects.get(
                        Q(user_id=user_id), Q(comment=comment))
                    is_liked = True
                except:
                    is_liked = False
                comment_data = {
                    'id': comment.id,
                    'comment_text': comment.comment_text,
                    'created_at': comment.created_at,
                    'user': {
                        'id': comment.user.id,
                        'username': comment.user.username,
                        'profile_image': f"{header}{domain}{comment.user.profile_picture.url}" if comment.user.profile_picture else None
                    },
                    'likes_count': LikeComment.objects.filter(Q(comment=comment)).count(),
                    'replies': [],
                    'is_Liked': is_liked
                }

                replies = CommentReply.objects.filter(comment=comment)
                for reply in replies:
                    try:
                        LikeCommentReply.objects.get(
                            Q(user_id=user_id), Q(comment_reply=reply))
                        is_re_liked = True
                    except:
                        is_re_liked = False

                    reply_data = {
                        'id': reply.id,
                        'reply_text': reply.reply_text,
                        'created_at': reply.created_at,
                        'user': {
                            'id': reply.user.id,
                            'username': reply.user.username,
                            'profile_image': f"{header}{domain}{reply.user.profile_picture.url}" if reply.user.profile_picture else None
                        },
                        'likes_count': LikeCommentReply.objects.filter(Q(comment_reply=reply)).count(),
                        'is_Liked': is_re_liked
                    }
                    comment_data['replies'].append(reply_data)
                data.append(comment_data)
            return Response({'status': True, 'data': data}, status=status.HTTP_200_OK)
        except Post.DoesNotExist:
            return Response({'status': False, 'message': 'Post not found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

    @sentry_sdk.trace
    def delete(self, request, id):
        auth_token = request.headers.get('Authorization')
        user_id = decode_token(auth_token)
        try:
            comment = Comment.objects.get(id=id, user_id=user_id)
            post = comment.post
            comment.delete()
            post.comments_count -= 1
            post.save()
            return Response({'status': True, 'message': 'Comment deleted successfully'}, status=status.HTTP_200_OK)
        except Comment.DoesNotExist:
            try:
                reply_comment = CommentReply.objects.get(
                    id=id, user_id=user_id)
                reply_comment.delete()
                return Response({'status': True, 'message': 'Comment deleted successfully'}, status=status.HTTP_200_OK)
            except CommentReply.DoesNotExist:
                return Response({'status': False, 'message': 'Comment not found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class LikeCommentView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def post(self, request, id):
        auth_token = request.headers.get('Authorization')
        user_id = decode_token(auth_token)
        try:
            comment = Comment.objects.get(id=id)
            like, created = LikeComment.objects.get_or_create(
                comment=comment, user_id=user_id)
            if created:
                return Response({'status': True, 'message': 'Comment liked successfully'}, status=status.HTTP_200_OK)
            else:
                like.delete()
                return Response({'status': True, 'message': 'Comment unliked successfully'}, status=status.HTTP_200_OK)
        except Comment.DoesNotExist:
            return Response({'status': False, 'message': 'Comment not found'}, status=status.HTTP_400_BAD_REQUEST)
        except UserRegistration.DoesNotExist:
            return Response({'status': False, 'message': 'User not found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class CommentReplyView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def post(self, request, id):
        auth_token = request.headers.get('Authorization')
        user_id = decode_token(auth_token)
        reply_text = request.data.get('reply_text')
        if not reply_text:
            return Response({'status': False, 'message': 'Reply text is required'}, status=status.HTTP_400_BAD_REQUEST)
        try:
            comment = Comment.objects.get(id=id)
            reply = CommentReply.objects.create(
                comment=comment, user_id=user_id, reply_text=reply_text)
            return Response({'status': True, 'message': 'Reply added successfully'}, status=status.HTTP_200_OK)
        except Comment.DoesNotExist:
            return Response({'status': False, 'message': 'Comment not found'}, status=status.HTTP_400_BAD_REQUEST)
        except UserRegistration.DoesNotExist:
            return Response({'status': False, 'message': 'User not found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

    @sentry_sdk.trace
    def get(self, request, id):
        auth_token = request.headers.get('Authorization')
        user_id = decode_token(auth_token)
        try:
            reply = CommentReply.objects.get(id=id, user_id=user_id)
            reply.delete()
            return Response({'status': True, 'message': 'Reply deleted successfully'}, status=status.HTTP_200_OK)
        except CommentReply.DoesNotExist:
            return Response({'status': False, 'message': 'Reply not found.'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class LikeCommentReplyView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    def post(self, request, id):
        auth_token = request.headers.get('Authorization')
        user_id = decode_token(auth_token)
        try:
            comment_reply = CommentReply.objects.get(id=id)
            like, created = LikeCommentReply.objects.get_or_create(
                comment_reply=comment_reply, user_id=user_id)
            if created:
                return Response({'status': True, 'message': 'Comment reply liked successfully'}, status=status.HTTP_200_OK)
            else:
                like.delete()
                return Response({'status': True, 'message': 'Comment reply unliked successfully'}, status=status.HTTP_200_OK)
        except CommentReply.DoesNotExist:
            return Response({'status': False, 'message': 'Comment reply not found'}, status=status.HTTP_400_BAD_REQUEST)
        except UserRegistration.DoesNotExist:
            return Response({'status': False, 'message': 'User not found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class HidePostView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    pagination_class = CustomPagination
    @sentry_sdk.trace
    def post(self, request):
        auth_token = request.headers.get('Authorization')
        user_id = decode_token(auth_token)
        post_id = request.data.get('post_id')
        try:
            post = Post.objects.get(id=post_id)
            if HidePost.objects.filter(Q(post=post), Q(user_id=user_id)):
                return Response({'status': False, 'message': 'Post is already hidden'}, status=status.HTTP_400_BAD_REQUEST)
            hide_post = HidePost.objects.create(post=post, user_id=user_id)
            return Response({'status': True, 'message': 'Post hidden successfully'}, status=status.HTTP_200_OK)
        except Post.DoesNotExist:
            return Response({'status': False, 'message': 'Post not found'}, status=status.HTTP_400_BAD_REQUEST)
        except UserRegistration.DoesNotExist:
            return Response({'status': False, 'message': 'User not found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

# SCHEDULE TASK


class UploadScheduledPost(APIView):
    def get(self, request):
        try:
            domain = request.get_host()
            if '127.0.0.1' in domain:
                header = 'https://'
            else:
                header = 'https://'
        except ValueError:
            header = ''
            domain = ''
        image_formats = [
            'jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'tif',
            'webp', 'svg', 'ico', 'heif', 'heic', 'raw', 'nef',
                    'cr2', 'orf', 'arw', 'dng'
        ]
        video_formats = [
            'mp4', 'mkv', 'mov', 'avi', 'flv', 'wmv', 'webm',
            'm4v', 'mpg', 'mpeg', '3gp', '3g2', 'mts', 'm2ts',
            'ts', 'ogv', 'rm', 'rmvb'
        ]
        posts = Post.objects.filter(Q(is_scheduled=True), Q(is_posted=False))
        statuses = {}
        
        for post in posts:
            print(post.pk)
            print(is_within_15_minutes(post.scheduled_at))
            if is_within_15_minutes(post.scheduled_at):
                try:
                    # Update post status
                    post.is_scheduled = False
                    post.is_posted = True
                    post.save()

                    # Get third party auth data
                    third_party_data = ThirdPartyAuth.objects.get(brand_id=post.brand_id)
                    
                    # Get all files for the post
                    post_files = PostFiles.objects.filter(post=post)
                    all_files = [file.file.url for file in post_files]
                    original_files = ['media/' + file.split('/media/')[-1] for file in all_files]

                    # Handle Instagram upload
                    if post.instagram and third_party_data.instagram_check:
                        try:
                            all_images = []
                            all_video = []
                            upload = False
                            for insta_files in all_files:
                                file_format = insta_files.split(".")[-1].split('.')[-1]
                                if file_format.lower() in image_formats:
                                    all_images.append(insta_files)
                                elif file_format.lower() in video_formats:
                                    all_video.append(insta_files)

                            if len(all_video) > 0:
                                upload, instagram_upload_id_video = upload_and_publish_instagram_video(
                                    third_party_data.insta_user_id, third_party_data.insta_auth_token, all_video[0], post.description)
                                if upload:
                                    post.instagram_id = instagram_upload_id_video

                            if len(all_images) > 0:
                                upload, instagram_upload_id = upload_images_to_instagram(
                                    third_party_data.insta_user_id, third_party_data.insta_auth_token, all_images, post.description)
                                if upload:
                                    post.instagram_id = instagram_upload_id
                            
                            statuses['Instagram'] = upload
                            post.save()
                        except Exception as e:
                            statuses['Instagram'] = False
                            print(f"Instagram upload error: {str(e)}")

                    # Handle Facebook upload
                    if post.facebook and third_party_data.facebook_check:
                        try:
                            all_images = []
                            all_video = []
                            upload = False
                            for facebook_files in all_files:
                                file_format = facebook_files.split(".")[-1].split('.')[-1]
                                if file_format.lower() in image_formats:
                                    all_images.append(facebook_files)
                                else:
                                    all_video.append(facebook_files)

                            if len(all_video) > 0:
                                upload, facebook_upload_id_video = upload_facebook_videos(
                                    third_party_data.facebook_page_id, third_party_data.facebook_token, all_video[0], post.description)
                                if upload:
                                    post.facebook_id = facebook_upload_id_video

                            if len(all_images) > 0:
                                upload, facebook_upload_id = upload_facebook_images(
                                    third_party_data.facebook_page_id, third_party_data.facebook_token, all_images, post.description)
                                if upload:
                                    post.facebook_id = facebook_upload_id

                            statuses['Facebook'] = upload
                            post.save()
                        except Exception as e:
                            statuses['Facebook'] = False
                            print(f"Facebook upload error: {str(e)}")

                    # Handle LinkedIn upload
                    if post.linkedin and third_party_data.linkedin_check:
                        try:
                            video_array = []
                            image_array = []
                            for data in original_files:
                                file_format = data.split(".")[-1].split('.')[-1]
                                if file_format.lower() in video_formats:
                                    video_array.append(data)
                                elif file_format.lower() in image_formats:
                                    image_array.append(data)

                            if len(video_array) > 0:
                                for upload_linkedin in video_array:
                                    media_types = [f'video/{upload_linkedin.split(".")[-1]}']
                                    upload_linkedin_video, uploaded_linkedin_id = create_linkedin_post_with_video(
                                        third_party_data.linked_in_token, third_party_data.linkedin_creds, post.description, [upload_linkedin], media_types)
                                    if upload_linkedin_video:
                                        post.linkedin_id = uploaded_linkedin_id
                                        statuses['LinkedIn'] = True

                            if len(image_array) > 0:
                                media_types = [f'image/{file.split(".")[-1]}' for file in image_array]
                                upload_linkedin_photo, uploaded_linkedin_id = create_linkedin_post_with_multiple_media(
                                    third_party_data.linked_in_token, third_party_data.linkedin_creds, post.description, original_files, media_types)
                                if upload_linkedin_photo:
                                    post.linkedin_id = uploaded_linkedin_id
                                    statuses['LinkedIn'] = True

                            post.save()
                        except Exception as e:
                            statuses['LinkedIn'] = False
                            print(f"LinkedIn upload error: {str(e)}")

                    # Handle Pinterest upload
                    if post.pinterest and third_party_data.pinterest_check:
                        try:
                            pin_image_array = []
                            for pin_data in all_files:
                                file_format = pin_data.split(".")[-1].split('.')[-1]
                                if file_format.lower() in image_formats:
                                    pin_image_array.append(pin_data)
                            
                            for upload_pin in pin_image_array:
                                pin_title = post.title if post.title else "Flowkar"
                                pin_description = post.description if post.description else "Flowkar"
                                pin_post, pin_id = post_pin(
                                    third_party_data.pinterest_creds, upload_pin, pin_title, pin_description)
                                if pin_post:
                                    post.pinterest_id = pin_id
                                    statuses['Pinterest'] = True
                                else:
                                    statuses['Pinterest'] = False
                            post.save()
                        except Exception as e:
                            statuses['Pinterest'] = False
                            print(f"Pinterest upload error: {str(e)}")

                    # Handle Vimeo upload
                    if post.vimeo and third_party_data.vimeo_check:
                        try:
                            vimeo_array = []
                            for vimeo_data in original_files:
                                file_format = vimeo_data.split(".")[-1].split('.')[-1]
                                if file_format.lower() in video_formats:
                                    vimeo_array.append(vimeo_data)
                            
                            for upload_vimeo in vimeo_array:
                                vimeo_title = post.title if post.title else "Flowkar"
                                vimeo_upload, vimeo_id = upload_vimeo_video(
                                    third_party_data.vimeo_creds, upload_vimeo, vimeo_title, post.description)
                                if vimeo_upload:
                                    post.vimeo_id = vimeo_id
                                    statuses['Vimeo'] = True
                                else:
                                    post.vimeo = False
                                    statuses['Vimeo'] = False
                            post.save()
                        except Exception as e:
                            statuses['Vimeo'] = False
                            print(f"Vimeo upload error: {str(e)}")

                    # Handle YouTube upload
                    if post.youtube and third_party_data.youtube_check:
                        try:
                            y_array = []
                            for y_data in original_files:
                                file_format = y_data.split(".")[-1].split('.')[-1]
                                if file_format.lower() in video_formats:
                                    y_array.append(y_data)

                            for upload_y in y_array:
                                y_title = post.title if post.title else "Flowkar"
                                pload, youtube_id = upload_video(
                                    creds=f'youtube/{post.brand_id}', file=upload_y, title=y_title, description=post.description)
                                if pload:
                                    post.youtube_id = youtube_id
                                    statuses['Youtube'] = True
                                else:
                                    post.youtube = False
                                    statuses['Youtube'] = False
                            post.save()
                        except Exception as e:
                            statuses['Youtube'] = False
                            print(f"YouTube upload error: {str(e)}")

                    # Handle TikTok upload
                    if post.dailymotion and third_party_data.tiktok_check:
                        try:
                            all_images = []
                            all_video = []
                            upload = False
                            for tiktok in all_files:
                                if tiktok.startswith("http://"):
                                    tiktok = tiktok.replace("http://", "https://", 1)
                                file_format = tiktok.split(".")[-1].split('.')[-1]
                                if file_format.lower() in image_formats:
                                    all_images.append(tiktok)
                                else:
                                    all_video.append(tiktok)
                            
                            if len(all_video) > 0:
                                upload, tiktok_upload_id_video = tiktok_upload_videos(
                                    third_party_data.tiktok_access_token, all_video, post.description)
                                if upload:
                                    post.tiktok_id = tiktok_upload_id_video
                                    statuses['TikTok'] = True
                                else:
                                    post.tiktok = False
                                    statuses['TikTok'] = False
                            post.save()
                        except Exception as e:
                            statuses['TikTok'] = False
                            print(f"TikTok upload error: {str(e)}")

                    # Handle Twitter/X upload
                    if post.twitter and third_party_data.thread_check:
                        try:
                            all_images = []
                            all_video = []
                            upload = False
                            for thread_files in all_files:
                                file_format = thread_files.split(".")[-1].split('.')[-1]
                                if file_format.lower() in image_formats:
                                    all_images.append(thread_files)
                                else:
                                    all_video.append(thread_files)
                            
                            if len(all_video) > 0:
                                upload, thread_upload_id_video = upload_and_publish_thread_video(
                                    third_party_data.thread_user_id, third_party_data.thread_auth_token, all_video[0], post.description)
                                if upload:
                                    post.twitter_id = thread_upload_id_video

                            if len(all_images) > 0:
                                upload, thread_upload_id = upload_images_to_thread(
                                    third_party_data.thread_user_id, third_party_data.thread_auth_token, all_images, post.description)
                                if upload:
                                    post.twitter_id = thread_upload_id
                            
                            statuses['Thread'] = upload
                            post.save()
                        except Exception as e:
                            statuses['Thread'] = False
                            print(f"Thread upload error: {str(e)}")

                    # Handle X (Twitter) upload
                    if post.x and third_party_data.x_check:
                        try:
                            access_token = get_and_update_x_token(third_party_data.x_refresh_token, post.brand_id)
                            all_images = []
                            all_video = []
                            upload = False
                            
                            for x_files in original_files:
                                file_format = x_files.split(".")[-1].split('.')[-1]
                                if file_format.lower() in image_formats:
                                    all_images.append(x_files)
                                else:
                                    all_video.append(x_files)
                            
                            if len(all_video) > 0:
                                upload, x_upload_id_video = upload_video_and_post_tweet(
                                    access_token, all_video, post.description)
                                if upload:
                                    post.x_id = x_upload_id_video
                            
                            if len(all_images) > 0:
                                upload, x_upload_id_image = post_tweet_with_asset(
                                    access_token, all_images, post.description)
                                if upload:
                                    post.x_id = x_upload_id_image
                            
                            statuses['X'] = upload
                            post.save()
                        except Exception as e:
                            statuses['X'] = False
                            print(f"X upload error: {str(e)}")

                    # Handle Tumblr upload
                    if post.tumblr and third_party_data.tumblr_check:
                        try:
                            all_images = []
                            all_video = []
                            for file in original_files:
                                file_format = file.split(".")[-1]
                                if file_format.lower() in video_formats:
                                    all_video.append(file)
                                elif file_format.lower() in image_formats:
                                    all_images.append(file)
                            
                            if len(all_images) > 0:
                                upload_tumblr, check_tumblr = upload_tumblr_photo(
                                    third_party_data.tumbler_token, third_party_data.tumbler_secret, all_images, post.description)
                                if check_tumblr:
                                    post.tumblr_id = upload_tumblr
                                    statuses['Tumblr'] = True
                                else:
                                    post.tumblr = False
                                    statuses['Tumblr'] = False
                            
                            if len(all_video) > 0:
                                for fi in all_video:
                                    upload_tumblr, check_tumblr = upload_tumblr_video(
                                        third_party_data.tumbler_token, third_party_data.tumbler_secret, fi, post.description)
                                    if check_tumblr:
                                        post.tumblr_id = upload_tumblr
                                        statuses['Tumblr'] = True
                                    else:
                                        post.tumblr = False
                                        statuses['Tumblr'] = False
                            post.save()
                        except Exception as e:
                            statuses['Tumblr'] = False
                            print(f"Tumblr upload error: {str(e)}")

                    # Handle Reddit upload
                    if post.reddit and third_party_data.reddit_check:
                        try:
                            if len(original_files) > 0:
                                upload_reddit, check_reddit = upload_photo_to_reddit(
                                    third_party_data.reddit_token, post.description, f'{header}{domain}/{original_files[0]}')
                                if check_reddit:
                                    statuses['Reddit'] = True
                                else:
                                    post.reddit = False
                                    statuses['Reddit'] = False
                            post.save()
                        except Exception as e:
                            statuses['Reddit'] = False
                            print(f"Reddit upload error: {str(e)}")

                    # Send notification to user
                    notify = send_notification('Post uploaded Successfully', 'Flowkar',
                                           [post.user.onesignal_player])
                    print(notify)

                    # Update points if needed
                    social_validated_data = {
                        'facebook': post.facebook,
                        'instagram': post.instagram,
                        'linkedin': post.linkedin,
                        'pinterest': post.pinterest,
                        'vimeo': post.vimeo,
                        'youtube': post.youtube,
                        'tiktok': post.dailymotion,
                        'threads': post.twitter,
                        'tumblr': post.tumblr,
                        'reddit': post.reddit
                    }
                    
                    is_any_post_social = check_platform_posts_last_24h(post.brand_id, social_validated_data)
                    social_referal_points = db_get_points("social_post_upload")
                    
                    for platform, is_allowed in is_any_post_social.items():
                        if is_allowed:
                            db_update_points(post.user_id, social_referal_points[0], "Social Post Upload Reward Credited")

                    is_any_post = check_user_posts_24h(post.brand_id)
                    if not is_any_post:
                        referal_points = db_get_points("upload_post")
                        db_update_points(post.user_id, referal_points[0], "Post Upload Reward Credited")

                except Exception as e:
                    print(f"Error processing scheduled post {post.id}: {str(e)}")
                    continue

        return Response({'status': True, 'message': 'Scheduled posts processed', 'data': statuses}, status=status.HTTP_200_OK)


class ReportUserView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            token_user_id = decode_token(auth_token)
            user_id = request.data.get('user_id')
            reason = request.data.get('reason')
            try:
                user = UserRegistration.objects.get(pk=user_id)
                try:
                    report_exist = Report.objects.get(
                        Q(reporting_user_id=token_user_id), Q(reported_user_id=user_id))
                    return Response({'status': False, 'message': 'User Already Reported By You'}, status=status.HTTP_400_BAD_REQUEST)
                except Report.DoesNotExist:
                    create_reported_user = Report.objects.create(
                        reporting_user_id=token_user_id,
                        reported_user=user,
                        reason=reason
                    )
                    user.report_count += 1
                    if user.report_count >= 5:
                        user.is_banned = True
                    user.save()
                    return Response({'status': True, 'message': 'User Reported Successfully'}, status=status.HTTP_200_OK)
            except UserRegistration.DoesNotExist:
                return Response({'status': False, 'message': 'User not found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class ReportPostView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            token_user_id = decode_token(auth_token)
            post_id = request.data.get('post_id')
            reason = request.data.get('reason')
            try:
                post = Post.objects.get(pk=post_id)
                try:
                    report_exist = ReportPost.objects.get(
                        Q(reporting_user_id=token_user_id), Q(reported_post=post_id))
                    return Response({'status': False, 'message': 'Post Already Reported By You'}, status=status.HTTP_400_BAD_REQUEST)
                except ReportPost.DoesNotExist:
                    create_reported_post = ReportPost.objects.create(
                        reporting_user_id=token_user_id,
                        reported_post=post,
                        reason=reason
                    )
                    HidePost.objects.create(
                        post=post,
                        user_id=token_user_id
                    )
                    post.report_count += 1
                    if post.report_count >= 10:
                        post.is_banned = True
                    post.save()
                    return Response({'status': True, 'message': 'Post Reported Successfully'}, status=status.HTTP_200_OK)
            except Post.DoesNotExist:
                return Response({'status': False, 'message': 'Post not found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


# ADMIN API

class UserDashboardView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            token_user_id = decode_token(auth_token)
            user_from_date = request.query_params.get('from_date')
            user_to_date = request.query_params.get('to_date')
            date = datetime.now().date()
            like_count = 0
            comments_count = 0
            likes = 0
            try:
                post = Post.objects.filter(user_id=token_user_id).exclude(
                    is_deleted=True).exclude(is_posted=False)
                followers = Follow.objects.filter(
                    to_user_id=token_user_id).count()
                post_count = post.count()
                for data in post:
                    try:
                        likes = LikePost.objects.filter(
                            post_id=data.pk).count()
                        like_count += likes
                        comments = Comment.objects.filter(
                            post_id=data.pk).count()
                        comments_count += comments
                    except (LikePost.DoesNotExist, Comment.DoesNotExist):
                        continue
                from_date = datetime.strptime(user_from_date, '%Y-%m-%d')
                to_date = datetime.strptime(user_to_date, '%Y-%m-%d')
                date_list = [
                    from_date + timedelta(days=x) for x in range((to_date - from_date).days + 1)]
                likes_data_list = []
                comments_data_list = []
                post_data_list = []
                i = 0

                for date in date_list:
                    i += 1
                    like_data = LikePost.objects.filter(
                        post__user_id=token_user_id,
                        created_on__year=date.year,
                        created_on__month=date.month,
                        created_on__day=date.day
                    ).aggregate(count=Count('id'))
                    likes_data_list.append({
                        'id': i,
                        'day': date.strftime('%A'),
                        'date': date.strftime('%d-%m-%Y'),
                        'count': like_data['count']
                    })
                    comment_data = Comment.objects.filter(
                        post__user_id=token_user_id,
                        created_at__year=date.year,
                        created_at__month=date.month,
                        created_at__day=date.day
                    ).aggregate(count=Count('id'))
                    comments_data_list.append({
                        'id': i,
                        'day': date.strftime('%A'),
                        'date': date.strftime('%d-%m-%Y'),
                        'count': comment_data['count']
                    })
                    post_data = Post.objects.filter(
                        user_id=token_user_id,
                        created_at__year=date.year,
                        created_at__month=date.month,
                        created_at__day=date.day
                    ).aggregate(count=Count('id'))
                    post_data_list.append({
                        'id': i,
                        'day': date.strftime('%A'),
                        'date': date.strftime('%d-%m-%Y'),
                        'count': post_data['count']
                    })
                return Response({'status': True, 'message': 'Data Found Successfully', 'dislplay_data': {'followers': followers, 'posts': post_count, 'likes': likes, 'comments': comments_count}, 'graph_data': {'likes': likes_data_list, 'comments': comments_data_list, 'posts': post_data_list}}, status=status.HTTP_200_OK)
            except (Post.DoesNotExist, Follow.DoesNotExist):
                post_count = 0
                followers = 0

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class SuperAdminDashBoardView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            token_user_id = decode_token(auth_token)
            date = datetime.now().date()
            try:
                user = UserRegistration.objects.get(pk=token_user_id)
                if user.is_admin == False:
                    return Response({'status': False, 'message': 'You Are Not An Admin'}, status=status.HTTP_400_BAD_REQUEST)
                else:
                    users = UserRegistration.objects.all()
                    posts = Post.objects.all()
                    total_user = users.count()
                    banned_user = 0
                    active_users = 0
                    new_users = 0
                    for user_search in users:
                        if user_search.is_banned == True:
                            banned_user += 1
                        if user_search.is_banned == False and user_search.is_deleted == False:
                            active_users += 1
                        if user_search.created_at.date() == date:
                            new_users += 1
                        else:
                            continue

                    total_post = posts.count()
                    reported_post = 0
                    banned_posts = 0
                    active_posts = 0
                    posts_with_counts = posts.annotate(
                        like_count=Count('likes'),
                        comment_count=Count('comments_count')
                    )

                    # Create an annotation for the combined likes and comments
                    posts_with_counts = posts_with_counts.annotate(
                        total_engagement=ExpressionWrapper(
                            F('like_count') + F('comment_count'),
                            output_field=IntegerField()
                        )
                    )

                    # Get the post with the highest total engagement
                    most_engaged_post = posts_with_counts.order_by(
                        '-total_engagement')[:5]
                    serializer = PostViewSerializer(
                        most_engaged_post, many=True, context={'request': request})
                    for post_search in posts:
                        if post_search.is_banned == True:
                            banned_posts += 1
                        if post_search.is_banned == False and post_search.is_deleted == False:
                            active_posts += 1
                        if post_search.report_count >= 1:
                            reported_post += 1
                        else:
                            continue
                    return Response({'status': True, 'message': 'Data Found Successfully',
                                     'users': {'total_user': total_user, 'banned_user': banned_user, 'active_users': active_users, 'new_users': new_users},
                                     'posts': {'total_post': total_post, 'reported_post': reported_post, 'banned_posts': banned_posts, 'active_posts': active_posts},
                                     'post': serializer.data
                                     })
            except UserRegistration.DoesNotExist:
                return Response({'status': False, 'message': 'User not found'}, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


# class UserManagementView(APIView):
#     # authentication_classes = [CustomJWTAuthentication]
#     pagination_class = CustomPagination

#     @sentry_sdk.trace
#     def get(self, request):
#         try:
#             from_date = request.query_params.get('from_date')
#             to_date = request.query_params.get('to_date')
#             users = UserRegistration.objects.all().order_by('id')
#             if from_date and to_date:
#                 users = UserRegistration.objects.filter(created_at__gte=from_date, created_at__lte=to_date).order_by('id')
#             users_in_last_24h = users.filter(created_at__gte=datetime.now() - timedelta(days=1)).count()
#             users_in_last_7d = users.filter(created_at__gte=datetime.now() - timedelta(days=7)).count()
#             users_in_last_30d = users.filter(created_at__gte=datetime.now() - timedelta(days=30)).count()
#             users_in_last_90d = users.filter(created_at__gte=datetime.now() - timedelta(days=90)).count()
#             users_in_last_180d = users.filter(created_at__gte=datetime.now() - timedelta(days=180)).count()
#             users_in_last_365d = users.filter(created_at__gte=datetime.now() - timedelta(days=365)).count()

#             additional_data = {
#                 'users_in_last_24h': users_in_last_24h,
#                 'users_in_last_7d': users_in_last_7d,
#                 'users_in_last_30d': users_in_last_30d,
#                 'users_in_last_90d': users_in_last_90d,
#                 'users_in_last_180d': users_in_last_180d,
#                 'users_in_last_365d': users_in_last_365d,
#             }
            
#             paginator = self.pagination_class()
#             user_data = []
#             try:
#                 domain = request.get_host()
#                 if '127.0.0.1' in domain:
#                     header = 'https://'
#                 else:
#                     header = 'https://'
#             except ValueError:
#                 header = ''
#                 domain = ''
#             for user in users:
#                 user_posts_count = Post.objects.filter(user=user).count()
#                 brand = Brands.objects.filter(user=user).first()
#                 third_party_auth = ThirdPartyAuth.objects.filter(brand=brand).first()
#                 connected_social_platforms = [
#                         third_party_auth.facebook_check,
#                         third_party_auth.instagram_check,
#                         third_party_auth.linkedin_check,
#                         third_party_auth.pinterest_check,
#                         third_party_auth.vimeo_check,
#                         third_party_auth.tiktok_check,
#                         third_party_auth.telegram_check,
#                         third_party_auth.mastodon_check,
#                         third_party_auth.youtube_check,
#                         third_party_auth.x_check,
#                         third_party_auth.reddit_check,
#                         third_party_auth.thread_check,
#                 ]
#                 count = 0
#                 for platform in connected_social_platforms:
#                     if platform == True:
#                         count += 1
#                 user_info = {
#                     'id': user.id,
#                     'profile_image': f"{header}{domain}{user.profile_picture.url}" if user.profile_picture else '',
#                     'username': user.username,
#                     'email': user.enc_email,
#                     'date': user.created_at,
#                     'posts': user_posts_count,
#                     'connected_social_platforms':count,
#                     'is_deleted': user.is_deleted,
#                 }
#                 user_data.append(user_info)
#             result_page = paginator.paginate_queryset(user_data[::-1], request)
#             return paginator.get_paginated_response({'status': True, 'message': 'Data Found Successfully', 'additional_data': additional_data, 'data': result_page})
#         except Exception as e:
#             return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)



class UserManagementView(APIView):
    # authentication_classes = [CustomJWTAuthentication]
    pagination_class = CustomPagination

    @sentry_sdk.trace
    def get(self, request):
        try:
            from_date = request.query_params.get('from_date')
            to_date = request.query_params.get('to_date')
            search = request.query_params.get('search')

            # Unfiltered users for additional_data
            all_users = UserRegistration.objects.all()

            users_in_last_24h = all_users.filter(created_at__gte=datetime.now() - timedelta(days=1)).count()
            users_in_last_7d = all_users.filter(created_at__gte=datetime.now() - timedelta(days=7)).count()
            users_in_last_30d = all_users.filter(created_at__gte=datetime.now() - timedelta(days=30)).count()
            users_in_last_90d = all_users.filter(created_at__gte=datetime.now() - timedelta(days=90)).count()
            users_in_last_180d = all_users.filter(created_at__gte=datetime.now() - timedelta(days=180)).count()
            users_in_last_365d = all_users.filter(created_at__gte=datetime.now() - timedelta(days=365)).count()

            additional_data = {
                'users_in_last_24h': users_in_last_24h,
                'users_in_last_7d': users_in_last_7d,
                'users_in_last_30d': users_in_last_30d,
                'users_in_last_90d': users_in_last_90d,
                'users_in_last_180d': users_in_last_180d,
                'users_in_last_365d': users_in_last_365d,
            }

            # Now apply filters for actual user data
            users = UserRegistration.objects.all()

            if from_date and to_date:
                users = users.filter(created_at__gte=from_date, created_at__lte=to_date)

            if search:
                users = users.filter(
                    Q(name__icontains=search) |
                    Q(username__icontains=search)
                )

            users = users.order_by('id')

            paginator = self.pagination_class()
            user_data = []

            try:
                domain = request.get_host()
                header = 'https://' if '127.0.0.1' in domain else 'https://'
            except ValueError:
                header = ''
                domain = ''

            for user in users:
                user_posts_count = Post.objects.filter(user=user).count()
                brand = Brands.objects.filter(user=user).first()
                third_party_auth = ThirdPartyAuth.objects.filter(brand=brand).first()

                count = 0
                if third_party_auth:
                    connected_social_platforms = [
                        third_party_auth.facebook_check,
                        third_party_auth.instagram_check,
                        third_party_auth.linkedin_check,
                        third_party_auth.pinterest_check,
                        third_party_auth.vimeo_check,
                        third_party_auth.tiktok_check,
                        third_party_auth.telegram_check,
                        third_party_auth.mastodon_check,
                        third_party_auth.youtube_check,
                        third_party_auth.x_check,
                        third_party_auth.reddit_check,
                        third_party_auth.thread_check,
                    ]
                    count = sum(1 for platform in connected_social_platforms if platform is True)

                user_info = {
                    'id': user.id,
                    'profile_image': f"{header}{domain}{user.profile_picture.url}" if user.profile_picture else '',
                    'username': user.username,
                    'email': user.enc_email,
                    'date': user.created_at,
                    'posts': user_posts_count,
                    'connected_social_platforms': count,
                    'is_deleted': user.is_deleted,
                }
                user_data.append(user_info)

            result_page = paginator.paginate_queryset(user_data[::-1], request)
            return paginator.get_paginated_response({
                'status': True,
                'message': 'Data Found Successfully',
                'additional_data': additional_data,
                'data': result_page
            })

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class DeleteUserView(APIView):
    # authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def get(self, request):
        auth_token = request.headers.get('Authorization')
        token_user_id = decode_token(auth_token)
        user_id = request.query_params.get('user_id')
        if user_id == None or user_id == '':
            return Response({'status': False, 'message': 'User ID is required'}, status=status.HTTP_400_BAD_REQUEST)
        try:
            user = UserRegistration.objects.get(pk=user_id)
            if user.is_deleted == True:
                return Response({'status': False, 'message': 'User Already Deleted'}, status=status.HTTP_400_BAD_REQUEST)
            user.is_deleted = True
            for post in Post.objects.filter(user=user):
                post.is_deleted = True
                post.save()
            user.save()
            return Response({'status': True, 'message': 'User deleted successfully'}, status=status.HTTP_200_OK)
        except UserRegistration.DoesNotExist:
            return Response({'status': False, 'message': 'User not found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class PostManagementView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    pagination_class = CustomPagination

    @sentry_sdk.trace
    def get(self, request):
        try:
            posts = Post.objects.all().order_by('id')
            paginator = self.pagination_class()
            result_page = paginator.paginate_queryset(posts, request)
            post_data = []
            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''
            for post in result_page:
                third_party_auth = ThirdPartyAuth.objects.get(
                    brand=post.brand)
                filtered_third_party_data = {
                    'facebook': third_party_auth.facebook_check if third_party_auth and third_party_auth.facebook_check else None,
                    'instagram': third_party_auth.instagram_check if third_party_auth and third_party_auth.instagram_check else None,
                    'linkedin': third_party_auth.linkedin_check if third_party_auth and third_party_auth.linkedin_check else None,
                    'pinterest': third_party_auth.pinterest_check if third_party_auth and third_party_auth.pinterest_check else None,
                    'vimeo': third_party_auth.vimeo_check if third_party_auth and third_party_auth.vimeo_check else None,
                }
                filtered_third_party_data = {
                    k: v for k, v in filtered_third_party_data.items() if v is not None}
                post_files = PostFiles.objects.filter(post_id=post.pk)
                post_file_list = []
                for post_file_search in post_files:
                    post_file_list.append(post_file_search.file.url)

                post_info = {
                    'post_id': post.id,
                    'post_count': post_files.count(),
                    'post_file': f'{header}{domain}{post_file_list[0]}',
                    'user_id': post.user.id,
                    'description': post.description,
                    'third_party_data': filtered_third_party_data,
                }
                post_data.append(post_info)
            return paginator.get_paginated_response({'status': True, 'message': 'Data Found Successfully', 'data': post_data})
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class DeletePostAdminView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def get(self, request):
        auth_token = request.headers.get('Authorization')
        token_user_id = decode_token(auth_token)
        post_id = request.query_params.get('post_id')
        if post_id is None or post_id == '':
            return Response({'status': False, 'message': 'Post ID is required'}, status=status.HTTP_400_BAD_REQUEST)
        try:
            post = Post.objects.get(pk=post_id)
            if post.is_deleted == True:
                return Response({'status': False, 'message': 'Post already deleted'}, status=status.HTTP_400_BAD_REQUEST)
            post.is_deleted = True
            post.save()
            return Response({'status': True, 'message': 'Post deleted successfully'}, status=status.HTTP_200_OK)
        except Post.DoesNotExist:
            return Response({'status': False, 'message': 'Post not found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class ScheduledPostApiView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def get(self, request):
        auth_token = request.headers.get('Authorization')
        token_user_id = decode_token(auth_token)
        unscheduled_post = request.query_params.get('unscheduled_post')
        date = datetime.now()
        tomorrow = datetime.now() + timedelta(days=1)
        formatted_date = date.strftime('%Y-%m-%d')
        month = date.strftime('%m')
        tomorrow_date = tomorrow.strftime('%Y-%m-%d')
        try:
            if unscheduled_post == '1':
                post_data = Post.objects.filter(Q(user_id=token_user_id), Q(is_scheduled=False), Q(
                    is_posted=False), Q(is_unscheduled=True))
            else:
                post_data = Post.objects.filter(
                    Q(user_id=token_user_id), Q(is_scheduled=True))
            today_posts = []
            tomorrow_posts = []
            this_month_posts = []
            for data in post_data:
                schedule_date = data.scheduled_at.split()[0]
                try:
                    which_month = schedule_date.split('-')[1]
                except IndexError:
                    which_month = ''
                if schedule_date == formatted_date:
                    today_posts.append(data)
                if schedule_date == tomorrow_date:
                    tomorrow_posts.append(data)
                if which_month == month:
                    this_month_posts.append(data)
            serializer = PostViewScheduleSerializer(
                post_data, many=True, context={'request': request})
            today = len(today_posts)
            tomorrow = len(tomorrow_posts)
            this_month = len(this_month_posts)
            return Response({'status': True, 'message': 'Data Found Successfully', 'today': today, 'tomorrow': tomorrow, 'this_month': this_month, 'data': serializer.data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class RemoveScheduledPostApiView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def get(self, request):
        auth_token = request.headers.get('Authorization')
        token_user_id = decode_token(auth_token)
        post_id = request.query_params.get('post_id')
        try:
            post_data = Post.objects.get(pk=post_id)
            post_data.is_scheduled = False
            post_data.is_unscheduled = True
            post_data.is_deleted = True
            post_data.save()
            return Response({'status': True, 'message': 'Post Unscheduled & Deleted Successfully'}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class UpdateTextPostView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)

            post_id = request.data.get('post_id')
            title = request.data.get('title')
            description = request.data.get('description')

            if not post_id:
                return Response({'status': False,'message': 'Post ID is required'}, status=status.HTTP_400_BAD_REQUEST)
            
            try:
                post = Post.objects.get(pk=post_id,user_id=user_id,is_text_post=True)
            except Post.DoesNotExist:
                return Response({'status': False,'message': 'Text post not found'}, status=status.HTTP_400_BAD_REQUEST)

            if title:
                post.title = title
            if description:
                post.description = description
            post.save()
            return Response({'status': True,'message': 'Post updated successfully'}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class SavePostView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    pagination_class = CustomPagination

    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            token_user_id = decode_token(auth_token)
            post_id = request.data.get('post_id')

            try:
                post = Post.objects.get(pk=post_id)
                try:
                    SavedPost.objects.get(
                        Q(post_id=post_id), Q(user_id=token_user_id))
                    return Response({'status': False, 'message': 'Post Already Saved', 'is_saved': True}, status=status.HTTP_400_BAD_REQUEST)
                except SavedPost.DoesNotExist:
                    SavedPost.objects.create(
                        post_id=post_id, user_id=token_user_id)
                    return Response({'status': True, 'message': 'Post Saved Successfully', 'is_saved': True}, status=status.HTTP_200_OK)
            except Post.DoesNotExist:
                return Response({'status': False, 'message': 'Post Not Found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            token_user_id = decode_token(auth_token)
            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''
            blocked_user_ids = Block.objects.filter(from_user_id=token_user_id).values_list('to_user_id', flat=True)
            saved_posts = SavedPost.objects.filter(user_id=token_user_id,post__is_deleted=False).exclude(post__user_id__in=blocked_user_ids).order_by('-created_on')
            all_data = []
            paginator = self.pagination_class()
            for data in saved_posts:
                id = data.post.pk
                title = data.post.title
                des = data.post.description
                location = data.post.location
                likes = LikePost.objects.filter(post_id=data.post.pk).count()
                dislikes = data.post.dislikes
                comment_count = data.post.comments_count
                tagged = data.post.tagged_in
                created_at = data.post.created_at
                files = [f'{header}{domain}{file.file.url}' for file in PostFiles.objects.filter(
                    Q(post_id=data.post.pk))]
                width, height = get_image_dimensions(files[0]) if files else (0, 0)
                post_user_id = data.post.user.pk
                username = data.post.user.username
                name = data.post.user.name
                profile_image = f'{header}{domain}{data.post.user.profile_picture.url}' if data.post.user.profile_picture else ''
                is_liked = False
                latest_comment = ''
                try:
                    comment = Comment.objects.filter(
                        post=data.post.pk).order_by('-pk').first()
                    if comment is not None:
                        latest_comment = f'{comment.user.username} {comment.comment_text}'
                    else:
                        latest_comment = ''
                except Comment.DoesNotExist:
                    latest_comment = ''

                try:
                    LikePost.objects.get(
                        Q(post_id=data.post.pk), Q(user_id=token_user_id))
                    is_liked = True
                except:
                    is_liked = False
                try:
                    SavedPost.objects.get(
                        Q(post_id=data.post.pk), Q(user_id=token_user_id))
                    is_saved = True
                except:
                    is_saved = False
                thumbail_files = []
                post_files = PostFiles.objects.filter(Q(post=data.post), Q(
                    is_video=True)).prefetch_related('postfilesthumbnail_set')
                for file in post_files:
                    # Assuming related_name is postfilesthumbnail_set
                    thumbnails = file.postfilesthumbnail_set.all()
                    thumbail_files.extend(
                        [f'{header}{domain}{thumbnail.file.url}' for thumbnail in thumbnails])
                append_object = {
                    'id': id,
                    'title': title,
                    'description': des,
                    'location': location,
                    'likes': likes,
                    'dislikes': dislikes,
                    'comments_count': comment_count,
                    'tagged_in': tagged,
                    'created_at': created_at,
                    'scheduled_at': data.post.scheduled_at if data.post.scheduled_at else '',
                    'files': files,
                    'width': width if width else 0,
                    'height': height if height else 0,
                    'thumbail_files': thumbail_files,
                    'latest_comment': latest_comment,
                    'user': {
                        'user_id': post_user_id,
                        'username': username,
                        'name': name,
                        'profile_image': profile_image
                    },
                    'is_liked': is_liked,
                    'is_saved': is_saved
                }
                all_data.append(append_object)

            result_page = paginator.paginate_queryset(all_data, request)
            return paginator.get_paginated_response({'status': True, 'message': 'Data Found Successfully', 'data': result_page})
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class AnalyticsView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def get(self, request):
        auth_token = request.headers.get('Authorization')
        token_user_id = decode_token(auth_token)
        # try:
        post_id = request.query_params.get('post_id')
        platform = request.query_params.get('platform')
        try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
        except ValueError:
                header = ''
                domain = ''
        try:
                post = Post.objects.get(pk=post_id)
                brand = post.brand.pk
                brand_auth = ThirdPartyAuth.objects.get(brand_id=brand)
                try:
                    LikePost.objects.get(
                        Q(post_id=post.pk), Q(user_id=token_user_id))
                    is_liked = True
                except:
                    is_liked = False
                print(f'post user id = {post.user.pk}')
                third_party_auth = ThirdPartyAuth.objects.get(
                    brand=post.brand.pk)
                show_objetct = {
                    'id': post.pk,
                    'user_id': post.user.pk,
                    'likes': LikePost.objects.filter(post_id=post.pk).count(),
                    'comments': Comment.objects.filter(post_id=post.pk).count(),
                    'files': [f'{header}{domain}{file.file.url}' for file in PostFiles.objects.filter(Q(post_id=post.pk))],
                    'is_like': is_liked
                }
                try:
                    if post.instagram == True and brand_auth.instagram_check == True:
                        likes, comment = get_instagram_likes_and_comments(
                            post.instagram_id, third_party_auth.insta_auth_token)
                        show_objetct['instagram'] = {
                            'like': likes,
                            'comments': comment
                        }
                    if post.twitter == True and brand_auth.thread_check == True:
                        likes, comment = get_thread_likes_and_comments(
                            post.twitter_id, third_party_auth.thread_auth_token)
                        show_objetct['Threads'] = {
                            'like': likes,
                            'comments': comment
                        }

                    if post.facebook == True and brand_auth.facebook_check == True:
                        likes, comment = get_facebook_likes_and_comments(
                            post.facebook_id, third_party_auth.facebook_token)
                        show_objetct['facebook'] = {
                            'like': likes,
                            'comments': comment
                        }

                    if post.youtube == True and brand_auth.youtube_check == True:
                        likes, comment = get_youtube_stats(
                            f'youtube/{brand}-oauth2.json', post.youtube_id, third_party_auth.youtube_refresh_token)
                        show_objetct['youtube'] = {
                            'like': int(likes),
                            'comments': int(comment)
                        }
                    if post.linkedin == True and brand_auth.linkedin_check == True:
                        linkedin_likes = linked_in_analytics_likes(
                            third_party_auth.linked_in_token, post.linkedin_id)
                        linkedin_comments = linked_in_analytics_comments(
                            third_party_auth.linked_in_token, post.linkedin_id)
                        show_objetct['linkedin'] = {
                            'like': linkedin_likes,
                            'comments': linkedin_comments
                        }
                    if post.pinterest == True and brand_auth.pinterest_check == True:
                        pinterest_data = get_pinterest_post_analytics(
                            post.pinterest_id, third_party_auth.pinterest_creds)
                        show_objetct['pinterest'] = {
                            'like': pinterest_data.get('TOTAL_REACTIONS', 0),
                            'comments': pinterest_data.get('TOTAL_COMMENTS', 0)
                        }

                    if post.vimeo == True and brand_auth.vimeo_check == True:
                        vimeo_id = post.vimeo_id.split('/')[-1]
                        vimeo_likes, vimeo_comment = get_vimeo_analytics(
                            third_party_auth.vimeo_creds, vimeo_id)
                        show_objetct['vimeo'] = {
                            'like': vimeo_likes,
                            'comments': vimeo_comment
                        }
                    if post.tumblr == True and brand_auth.tumblr_check == True:
                        tumblr_post_id = int(post.tumblr_id)
                        tumbler_like, tumblr_comments = get_tumblr_analytics(
                            third_party_auth.tumbler_token, third_party_auth.tumbler_secret, tumblr_post_id)
                        show_objetct['tumblr'] = {
                            'like': tumbler_like,
                            'comments': tumblr_comments
                        }
                    return Response({'status': True, 'message': 'Data Found Successfully', 'data': show_objetct}, status=status.HTTP_200_OK)
                except AttributeError:
                    return Response({'status': True, 'message': 'Data Found Successfully', 'data': show_objetct}, status=status.HTTP_200_OK)
        except Post.DoesNotExist:
                return Response({'status': False, 'message': 'Post Not Found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

# NOTIFICATION


class GetNotificationView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    pagination_class = CustomPagination
    
    @sentry_sdk.trace
    def get(self, request):
        auth_token = request.headers.get('Authorization')
        user_id = decode_token(auth_token)
        paginator = self.pagination_class()
        try:
            domain = request.get_host()
            if '127.0.0.1' in domain:
                header = 'https://'
            else:
                header = 'https://'
        except ValueError:
            header = ''
            domain = ''
        try:
            notification_data = Notification.objects.filter(
                user_id=user_id).order_by('-created_at')
            processed_inviters = set()
            show_data = []
            for data in notification_data:
                if 'post' in data.type:

                    if data.post and data.post.is_deleted:
                        continue

                    append_data = {
                        'id': data.pk,
                        'type': data.type,
                        'title': data.title,
                        'message': data.message,
                        'created_at': data.created_at,
                        'user': {
                            'user_id': data.from_user.pk,
                            'profile_image': f'{header}{domain}{data.from_user.profile_picture.url}'if data.from_user.profile_picture else '',
                        },
                        'post': {
                            'id': data.post.pk if data.post else '',
                            'post_files': [f'{header}{domain}{file.file.url}' for file in PostFiles.objects.filter(post_id=data.post.pk)] if data.post else ''
                        }
                    }
                elif data.type == 'user_invited':
                    inviter_id = data.from_user.pk
                    
                    if inviter_id in processed_inviters:
                        continue
                    
                    processed_inviters.add(inviter_id)

                    user_management_list = UserManagement.objects.filter(user_invited_id=user_id,user_id=inviter_id,is_accepted=False)
                    
                    # if not user_management_list.exists():
                    #     continue
                    
                    invited_user_info_list = []
                    
                    for user_management in user_management_list:
                        invited_user_info = {
                            'id': user_management.pk, 
                            'username': data.from_user.name,
                            'profile_image': f'{header}{domain}{data.from_user.profile_picture.url}' if data.from_user.profile_picture else '',
                            'is_accepted': user_management.is_accepted, 
                        }
                        invited_user_info_list.append(invited_user_info)
                    
                    append_data = {
                        'id': data.pk,
                        'type': data.type,
                        'title': data.title,
                        'message': data.message,
                        'created_at': data.created_at,
                        'invited_users': invited_user_info_list 
                    }
                    
                    if data.pk in [i['id'] for i in show_data]:
                        pass
                    else:
                        show_data.append(append_data)
                else:

                    if data.post and data.post.is_deleted:
                        continue

                    append_data = {
                        'id': data.pk,
                        'type': data.type,
                        'title': data.title,
                        'message': data.message,
                        'created_at': data.created_at,
                        'user': {
                            'user_id': data.from_user.pk,
                            'profile_image': f'{header}{domain}{data.from_user.profile_picture.url}'if data.from_user.profile_picture else '',
                        },
                        'post': {
                            'id': data.post.pk if data.post else 0,
                            'post_files': [f'{header}{domain}{file.file.url}' for file in PostFiles.objects.filter(post_id=data.post.pk)] if data.post else []
                        }
                    }

                if data.pk in [i['id'] for i in show_data]:
                    pass
                else:
                    show_data.append(append_data)
            result_page = paginator.paginate_queryset(show_data, request)
            return paginator.get_paginated_response({'status': True, 'message': 'Data Found Successfully', 'data': result_page})
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class DeleteNotificationView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)

            notification_id = request.data.get('notification_id')

            if not notification_id:
                return Response({'status': False,'message': 'notification id is required.'}, status=status.HTTP_400_BAD_REQUEST)

            notification = Notification.objects.get(pk=notification_id, user_id=user_id)
            notification.delete()
            return Response({'status': True,'message': 'Notification deleted successfully.'}, status=status.HTTP_200_OK)
        except Notification.DoesNotExist:
            return Response({'status': False,'message': 'Notification not found.'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False,'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        
        
class ChatListView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    pagination_class = CustomPagination
    @sentry_sdk.trace
    def get(self, request):
        auth_token = request.headers.get('Authorization')
        logged_in_user_id = decode_token(auth_token)
        brand_id = request.headers.get('brand')

        local_user_id = request.headers.get('user')
        role_status, role_permission_result = check_multiple_roles_permissions(local_user_id, brand_id)
        if role_status == False:
            return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)

        if 2 not in role_permission_result :
            return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
        try:

            domain = request.get_host()
            header = 'https://' if '127.0.0.1' in domain else 'https://'

            blocked_users = set(Block.objects.filter(from_user=logged_in_user_id).values_list('to_user_id', flat=True))
            from_blocked_users = set(Block.objects.filter(to_user=logged_in_user_id).values_list('from_user_id', flat=True))
            all_blocked_users = blocked_users.union(from_blocked_users)

            # Exclude messages deleted by the current user
            latest_messages = ChatMessage.objects.filter(
                Q(from_user_id=logged_in_user_id) | Q(to_user_id=logged_in_user_id)
            ).exclude(
                Q(from_user_id=logged_in_user_id, deleted_by_from_user=True) |
                Q(to_user_id=logged_in_user_id, deleted_by_to_user=True)
            ).values(
                'from_user_id', 'to_user_id'
            ).annotate(
                last_msg_time=Max('created_at')
            ).order_by('-last_msg_time')

            chat_list = []
            for msg in latest_messages:
                other_user_id = msg['from_user_id'] if msg['from_user_id'] != logged_in_user_id else msg['to_user_id']
                if other_user_id in all_blocked_users:
                    continue
                user = UserRegistration.objects.get(pk=other_user_id)

                if any(chat['user_id'] == user.pk for chat in chat_list):
                    continue
                if user.pk == logged_in_user_id:
                    continue

                last_message = ChatMessage.objects.filter(
                    from_user_id=msg['from_user_id'],
                    to_user_id=msg['to_user_id'],
                    created_at=msg['last_msg_time']
                ).exclude(
                    Q(from_user_id=logged_in_user_id, deleted_by_from_user=True) |
                    Q(to_user_id=logged_in_user_id, deleted_by_to_user=True)
                ).first()

                is_read = True
                if last_message.to_user_id == logged_in_user_id:
                    is_read = last_message.is_read

                if last_message.type == 'share_post':
                    try:
                        post_id = int(last_message.messages)
                        post = Post.objects.get(pk=post_id)
                        post_owner_name = post.user.username
                    except (Post.DoesNotExist, ValueError, TypeError):
                        post_owner_name = 'a post'

                    if str(logged_in_user_id) == str(last_message.from_user_id):
                        latest_msg_text = "You sent a attachment"
                    else:
                        latest_msg_text = f"sent a attachment by {post_owner_name}"
                else:
                    latest_msg_text = last_message.messages

                chat_list.append({
                    'user_id': user.pk,
                    'profile_image': f'{user.profile_picture.url}' if user.profile_picture else '',
                    'user_name': user.username,
                    'name':user.name,
                    'latest_message': latest_msg_text,
                    'to_user': last_message.to_user.pk,
                    'created_at': timezone.localtime(last_message.created_at).isoformat(),
                    'is_read': is_read
                })


            # The list is already sorted by 'created_at' descending from the query
            paginator = self.pagination_class()
            paginated_chat_list = paginator.paginate_queryset(chat_list, request)
            return paginator.get_paginated_response({
                'status': True,
                'message': 'Data Found Successfully',
                'data': paginated_chat_list
            })

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class DeleteChatListApi(APIView):
    authentication_classes = [CustomJWTAuthentication]
    pagination_class = CustomPagination
    @sentry_sdk.trace
    def get(self, request):
        auth_token = request.headers.get('Authorization')
        logged_in_user_id = decode_token(auth_token)
        user_id = request.query_params.get('user_id')
        try:
            # Get messages between logged_in_user and the specified user
            messages = ChatMessage.objects.filter(
                Q(from_user_id=logged_in_user_id, to_user_id=user_id) |
                Q(from_user_id=user_id, to_user_id=logged_in_user_id)
            )

            # Mark messages as deleted for the logged-in user only
            for message in messages:
                if message.from_user_id == logged_in_user_id:
                    # If logged-in user is the sender, mark as deleted by from_user
                    message.deleted_by_from_user = True
                else:
                    # If logged-in user is the receiver, mark as deleted by to_user
                    message.deleted_by_to_user = True
                message.save()

            return Response({
                'status': True,
                'message': 'Chat Deleted Successfully',
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class MessageListView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    pagination_class = CustomPagination

    @sentry_sdk.trace
    def get(self, request):
        try:
            domain = request.get_host()
            if '127.0.0.1' in  domain:
                header = 'https://'
            else:
                header = 'https://'
        except ValueError:
                header = ''
                domain = ''
        auth_token = request.headers.get('Authorization')
        logged_in_user_id = decode_token(auth_token)
        chat_user_id = request.query_params.get('chat_user_id')

        if not chat_user_id:
            return Response({'status': False, 'message': 'chat_user_id is required.'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            messages = ChatMessage.objects.filter(
                (Q(from_user_id=logged_in_user_id) & Q(to_user_id=chat_user_id)) |
                (Q(from_user_id=chat_user_id) & Q(to_user_id=logged_in_user_id))
            ).exclude(
                Q(from_user_id=logged_in_user_id, deleted_by_from_user=True) |
                Q(to_user_id=logged_in_user_id, deleted_by_to_user=True)
            ).order_by('-created_at', '-id')

            message_list = []

            for msg in messages:
                created_at_local = timezone.localtime(msg.created_at).isoformat()
                message_data = {
                    'id': msg.id,
                    'type': msg.type,
                    'is_read': msg.is_read,
                    'created_at': created_at_local,
                    'sent_by': msg.from_user.pk
                }

                if msg.type in ['text', 'message']:
                    message_data['message'] = msg.messages

                elif msg.type in ['image', 'voice', 'custom']:
                    message_data['message'] = f'{msg.file}'
                    message_data['file'] = f'{msg.file}'

                elif msg.type == 'share_post':
                    message_data['message'] = msg.messages
                    try:
                        post_id = msg.messages.strip() if msg.messages else None
                        
                        if not post_id and hasattr(msg, 'message_id') and msg.message_id:
                            post_id = str(msg.message_id).strip()
                        
                        if post_id:
                            post = Post.objects.get(pk=int(post_id), is_deleted=False)
                            post_file = PostFiles.objects.filter(post=post).prefetch_related('postfilesthumbnail_set').first()
                            post_owner = post.user

                            video_url = post_file.file.url
                            try:
                                video_url = post_file.file.url if post_file and post_file.file else ''
                            except:
                                video_url = ''

                            if post_file:
                                if post_file.is_video:
                                    if post_file.thumbnail:
                                        media_url = post_file.thumbnail.url
                                    else:
                                        first_thumb = post_file.postfilesthumbnail_set.first()
                                        media_url = first_thumb.file.url if first_thumb else ''
                                else:
                                    media_url = post_file.file.url
                            else:
                                media_url = ''
                            
                            try:
                                LikePost.objects.get(post_id=post.pk, user_id=logged_in_user_id)
                                is_liked = True
                            except LikePost.DoesNotExist:
                                is_liked = False
                                
                            likes_count = LikePost.objects.filter(post_id=post.pk).count()
                            message_data.update({
                                'post_id': post.pk,
                                'post_media_url': media_url,
                                'likes_count': likes_count,
                                'is_liked': is_liked,
                                'file': [f"{header}{domain}{video_url}"] if video_url else [],
                                'title': post.title if post.is_text_post else '',
                                'description': post.description if post.is_text_post else '',
                                'post_user': {
                                    'id': post_owner.id,
                                    'username': post_owner.username,
                                    'name': post_owner.name,
                                    'profile_image': post_owner.profile_picture.url if post_owner.profile_picture and hasattr(post_owner.profile_picture, 'url') else ''
                                }
                            })
                        else:
                            message_data['post_error'] = 'Invalid post reference - no valid ID found'


                    except (Post.DoesNotExist, ValueError, TypeError) as e:
                        message_data['post_error'] = f'Post not found or invalid reference: {str(e)}'


                message_list.append(message_data)

            paginator = self.pagination_class()
            paginated_message_list = paginator.paginate_queryset(message_list, request)
            return paginator.get_paginated_response({
                'status': True,
                'message': 'Data Found Successfully',
                'data': paginated_message_list
            })

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        


class OneSignalView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    pagination_class = CustomPagination
    @sentry_sdk.trace

    def post(self, request):
        auth_token = request.headers.get('Authorization')
        onesignal_id = request.data.get('onesignal_id')
        logged_in_user_id = decode_token(auth_token)
        data = UserRegistration.objects.get(pk=logged_in_user_id)
        try:
            if onesignal_id is not "" :
                data.onesignal_player = onesignal_id
                data.save()
            return Response({'status': True, 'message': 'Onesignal Id Stored Successfully'}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class ShareProfileView(APIView):
    @sentry_sdk.trace
    def get(self, request):
        try:
            domain = request.get_host()
            if '127.0.0.1' in  domain:
                header = 'https://'
            else:
                header = 'https://'
        except ValueError:
                header = ''
                domain = ''
        brand_id = request.headers.get('brand')
        if len(brand_id) >= 90:
            brand_id = decrypt_data(brand_id)
        try:

            third_party_auth = ThirdPartyAuth.objects.get(
                brand_id=brand_id)
            

            profile_data = {
                "username": third_party_auth.brand.user.username,
                "name": third_party_auth.brand.user.name,
                "profile_picture": f"{header}{domain}{third_party_auth.brand.user.profile_picture.url}" if third_party_auth.brand.user.profile_picture else '',
                "social_links": [],
                "brand_detail":{
                    "name":third_party_auth.brand.name,
                    "brand_icon":f"{header}{domain}{third_party_auth.brand.logo.url}" if third_party_auth.brand.logo else "",
                    "domain":third_party_auth.brand.domain if third_party_auth.brand.domain else "",
                    "email":third_party_auth.brand.email if third_party_auth.brand.email else ""
                }
            }
            social_platforms = SocialPlatforms.objects.get(pk=1)



            social_object ={
                'facebook':social_platforms.facebook,
                'instagram': social_platforms.instagram,
                'thread':  social_platforms.threads,
                'linkedin':  social_platforms.linkedin,
                'pintrest': social_platforms.pinterest,
                'tumblr':  social_platforms.tumblr,
                'Reddit':  social_platforms.reddit,
                'Youtube': social_platforms.youtube,
                'tiktok':social_platforms.tiktok,
                'x':'1',
                'telegram':'0',
                'mastodon':'0',
            }

            if third_party_auth:

                if third_party_auth.instagram_check:
                    insta_data = get_instagram_user_info(
                        third_party_auth.insta_auth_token, third_party_auth.insta_user_id)
                    username = insta_data.get('username')
                    profile_data["social_links"].append({
                        "platform": "instagram",
                        "url": f"https://www.instagram.com/{username}",
                        "name": third_party_auth.brand.user.name,
                        "username": username,
                        "profile_image": insta_data.get('profile_picture_url', ''),
                        'platform_status':social_platforms.instagram,
                        'user_status':third_party_auth.instagram_check,
                    })
                else:
                    profile_data["social_links"].append({
                        "platform": "instagram",
                        "url": f"",
                        "name": '',
                        "username": '',
                        "profile_image":'',
                        'platform_status':social_platforms.instagram,
                        'user_status':third_party_auth.instagram_check,

                    })

                if third_party_auth.x_check:
                    access_token = get_and_update_x_token(third_party_auth.x_refresh_token, brand_id)
                    x_status , x_data = get_x_user_info(
                        access_token)
                    print(x_data)
                    if x_status:
                        username = x_data.get('username')
                        profile_data["social_links"].append({
                            "platform": "x",
                            "url": f"https://www.x.com/{username}",
                            "name": third_party_auth.brand.user.name,
                            "username": username,
                            "profile_image": x_data.get('profile_picture_url', ''),
                            'platform_status':'1',
                            'user_status':third_party_auth.x_check,
                        })
                    else:
                        profile_data["social_links"].append({
                            "platform": "x",
                            "url": f"",
                            "name": '',
                            "username": '',
                            "profile_image":'',})
                else:
                    profile_data["social_links"].append({
                        "platform": "x",
                        "url": f"",
                        "name": '',
                        "username": '',
                        "profile_image":'',
                        'platform_status':"1",
                        'user_status':third_party_auth.x_check,

                    })

                if third_party_auth.vimeo_check:
                    vimeo_profile = get_vimeo_profile(
                        third_party_auth.vimeo_creds)
                    profile_data["social_links"].append({
                        "platform": "vimeo",
                        "url": vimeo_profile.get('profile_url', None),
                        "name": vimeo_profile.get('name', ''),
                        "username": vimeo_profile.get('username', ''),
                        "profile_image": vimeo_profile.get('profile_image', ''),
                        'platform_status':social_platforms.vimeo,
                        'user_status':third_party_auth.vimeo_check,

                    })
                else:
                    profile_data["social_links"].append({
                        "platform": "vimeo",
                        "url": '',
                        "name": '',
                        "username": '',
                        "profile_image": '',
                        'platform_status':social_platforms.vimeo,
                        'user_status':third_party_auth.vimeo_check,

                    })
                if third_party_auth.pinterest_check:
                    pinterest_profile = get_pinterest_profile(
                        third_party_auth.pinterest_creds)
                    profile_data["social_links"].append({
                        "platform": "pinterest",
                        "url": pinterest_profile.get('profile_url', ''),
                        "name": pinterest_profile.get('name', ''),
                        "username": pinterest_profile.get('username', ''),
                        "profile_image": pinterest_profile.get('profile_image', ''),
                        'platform_status':social_platforms.pinterest,
                        'user_status':third_party_auth.pinterest_check,

                    })
                else:
                    profile_data["social_links"].append({
                        "platform": "pinterest",
                        "url": '',
                        "name": '',
                        "username": '',
                        "profile_image": '',
                        'platform_status':social_platforms.pinterest,
                        'user_status':third_party_auth.pinterest_check,

                    })
                if third_party_auth.linkedin_check:
                    linkedin_profile = get_linkedin_profile(
                        third_party_auth.linked_in_token)
                    profile_data["social_links"].append({
                        "platform": "linkedin",
                        "url": linkedin_profile.get("profile_url", ''),
                        "name": linkedin_profile.get('name', ''),
                        "username": linkedin_profile.get('username', ''),
                        "profile_image": linkedin_profile.get('profile_picture', ''),
                        'platform_status':social_platforms.linkedin,
                        'user_status':third_party_auth.linkedin_check,

                    })
                else:
                    profile_data["social_links"].append({
                        "platform": "linkedin",
                        "url": '',
                        "name": '',
                        "username": '',
                        "profile_image": '',
                        'platform_status':social_platforms.linkedin,
                        'user_status':third_party_auth.linkedin_check,

                    })
                if third_party_auth.reddit_check:
                    reddit_profile = get_reddit_profile(
                        third_party_auth.reddit_token)
                    profile_data["social_links"].append({
                        "platform": "reddit",
                        "url": reddit_profile.get('profile_url', ''),
                        "name": reddit_profile.get('name', ''),
                        "username": reddit_profile.get('username', ''),
                        "profile_image": reddit_profile.get('profile_image', ''),
                        'platform_status':social_platforms.reddit,
                        'user_status':third_party_auth.reddit_check,

                    })
                else:
                    profile_data["social_links"].append({
                        "platform": "reddit",
                        "url": '',
                        "name": '',
                        "username": '',
                        "profile_image": '',
                        'platform_status':social_platforms.reddit,
                        'user_status':third_party_auth.reddit_check,

                    })
                if third_party_auth.tumblr_check:
                    tumblr_data = get_tumblr_profile_data(
                        third_party_auth.tumbler_token, third_party_auth.tumbler_secret)
                    profile_data["social_links"].append({
                        "platform": "tumblr",
                        "url": tumblr_data.get('profile_url', ''),
                        "name": tumblr_data.get('name', ''),
                        "username": tumblr_data.get('username', ''),
                        "profile_image": tumblr_data.get('profile_image', ''),
                        'platform_status':social_platforms.tumblr,
                        'user_status':third_party_auth.tumblr_check

                    })
                else:
                    profile_data["social_links"].append({
                        "platform": "tumblr",
                        "url": '',
                        "name": '',
                        "username": '',
                        "profile_image": '',
                        'platform_status':social_platforms.tumblr,
                        'user_status':third_party_auth.tumblr_check

                    })

                if third_party_auth.thread_check:
                    thread_data = get_threads_user_info(
                        third_party_auth.thread_auth_token)
                    profile_data["social_links"].append({
                        "platform": "thread",
                        "url": thread_data.get('threads_profile_url', ''),
                        "name": third_party_auth.brand.user.name,
                        "username": thread_data.get('username', ''),
                        "profile_image": thread_data.get('profile_picture_url', ''),
                        'platform_status':social_platforms.threads,
                        'user_status':third_party_auth.thread_check

                    })
                else:
                    profile_data["social_links"].append({
                        "platform": "thread",
                        "url": '',
                        "name": '',
                        "username": '',
                        "profile_image": '',
                        'platform_status':social_platforms.threads,
                        'user_status':third_party_auth.thread_check

                    })

                if third_party_auth.facebook_check:
                    facebook_data = get_facebook_user_info(third_party_auth.facebook_token)

                    profile_data["social_links"].append({
                        "platform": "facebook",
                        "url": facebook_data.get('profile_url', ''),
                        "name": facebook_data.get('name', ''),
                        "username": facebook_data.get('name', ''),
                        "profile_image": facebook_data.get('profile_image', ''),
                        "platform_status": social_platforms.facebook,
                        "user_status": third_party_auth.facebook_check
                    })

                else:
                    profile_data["social_links"].append({
                        "platform": "facebook",
                        "url": '',
                        "name": '',
                        "username": '',
                        "profile_image": '',
                        'platform_status':social_platforms.facebook,
                        'user_status':third_party_auth.facebook_check

                    })

                if third_party_auth.tiktok_check:
                    profile_data["social_links"].append({
                        "platform": "tiktok",
                        "url":'',
                        "name": '',
                        "username": '',
                        "profile_image": '',
                        'platform_status':social_platforms.tiktok,
                        'user_status':third_party_auth.tiktok_check

                    })
                
                else:
                    profile_data["social_links"].append({
                        "platform": "tiktok",
                        "url": '',
                        "name": '',
                        "username": '',
                        "profile_image": '',
                        'platform_status':social_platforms.tiktok,
                        'user_status':third_party_auth.tiktok_check

                    })

                if third_party_auth.youtube_check:
                    youtube_status, youtube_data = get_channel_details(f'youtube/{brand_id}-oauth2.json',third_party_auth.youtube_refresh_token)
                    if youtube_status:
                        channel_url = f"https://www.youtube.com/{youtube_data.get('customUrl')}"


            
                        profile_data["social_links"].append({
                                "platform": "youtube",
                                "url": channel_url,
                                "name": youtube_data.get("title"),
                                "username": youtube_data.get("title"),
                                "profile_image": youtube_data.get("profile_image"),
                                'platform_status':social_platforms.youtube,
                                'user_status':third_party_auth.youtube_check

                            })
                    else:
                        profile_data["social_links"].append({
                        "platform": "youtube",
                        "url": '',
                        "name": '',
                        "username": '',
                        "profile_image": '',
                        'platform_status':social_platforms.youtube,
                        'user_status':False

                    })
                         
                else:
                    profile_data["social_links"].append({
                        "platform": "youtube",
                        "url": '',
                        "name": '',
                        "username": '',
                        "profile_image": '',
                        'platform_status':social_platforms.youtube,
                        'user_status':False

                    })
                
                if third_party_auth.telegram_check:
                    telegram_data = get_telegram_user_info(third_party_auth.brand.user.pk,brand_id)
                    profile_data["social_links"].append({
                            "platform": "telegram",
                            "url": '',
                            "name":telegram_data.get('first_name','') ,
                            "username": telegram_data.get('first_name',''),
                            "profile_image": '',
                            'platform_status':"1",
                            'user_status':third_party_auth.telegram_check

                        })
                else:
                    profile_data["social_links"].append({
                            "platform": "telegram",
                            "url": '',
                            "name": '',
                            "username": '',
                            "profile_image": '',
                            'platform_status':"1",
                            'user_status':False

                        })
                
                if third_party_auth.mastodon_check:
                    mastodon_data = get_mastodon_user_info('https://mastodon.social', third_party_auth.mastodon_token)
                    if mastodon_data:
                        profile_data["social_links"].append({
                            "platform": "mastodon",
                            "url": mastodon_data.get('url', ''),
                            "name": mastodon_data.get('display_name', ''),
                            "username": mastodon_data.get('username', ''),
                            "profile_image": mastodon_data.get('avatar', ''),
                            'platform_status':"1",
                            'user_status':third_party_auth.mastodon_check
                        })
                    else:
                        profile_data["social_links"].append({
                            "platform": "mastodon",
                            "url": '',
                            "name": '',
                            "username": '',
                            "profile_image": '',
                            'platform_status':"1",
                            'user_status':False
                        })
                else:
                    profile_data["social_links"].append({
                        "platform": "mastodon",
                        "url": '',
                        "name": '',
                        "username": '',
                        "profile_image": '',
                        'platform_status':"1",
                        'user_status':False
                    })

            return Response({"status": True, "profile": profile_data}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"status": False, "message": f"Error: {e}"}, status=status.HTTP_400_BAD_REQUEST)


class GetShareProfileQRView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''
            try:
                user_brand = CurrentBrand.objects.get(user_id=user_id)
                brand_id = encrypt_data(str(user_brand.brand.pk))
                qr_code = generate_qr_code(
                    f'https://flowkar.com/share-profile/?brand_id={brand_id}', brand_id)
                return Response({'status': True, 'message': 'QR Generated Successfully', 'QR': f'{header}{domain}/{qr_code}', 'url': f'https://flowkar.com/share-profile/?brand_id={brand_id}'}, status=status.HTTP_200_OK)
            except UserRegistration.DoesNotExist:
                return Response({'status': False, 'message': 'User Not Found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error: {e}'}, status=status.HTTP_400_BAD_REQUEST)


class InstaProfile(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    def get(self, request):
        auth_token = request.headers.get('Authorization')
        brand_id = request.headers.get('brand')
        user_id = decode_token(auth_token)
        try:
            profile_data = {}
            third_party_auth = ThirdPartyAuth.objects.get(
                brand_id=brand_id)
            if third_party_auth.instagram_check:
                insta_data = get_instagram_user_info(
                    third_party_auth.insta_auth_token, third_party_auth.insta_user_id)
                username = insta_data.get('username')
                name = insta_data.get('name')
                followers_count = insta_data.get('followers_count')
                follows_count = insta_data.get('follows_count')
                follows_count = insta_data.get('follows_count')
                profile_picture_url = insta_data.get('profile_picture_url')
                media = insta_data.get('media')
                likes, comment, media_data = get_instagram_likes_and_comments_url(
                    media, third_party_auth.insta_auth_token)
                profile_data["data"] = {
                    "username": username,
                    "name": name,
                    "followers_count": followers_count,
                    "follows_count": follows_count,
                    "profile_image": profile_picture_url,
                    "profile_image": profile_picture_url,
                    "post": {
                        'likes': likes,
                        'comment': comment,
                        'media_data': media_data
                    }
                }

            return Response({"status": True, "data": profile_data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"status": False, "message": f"Error: {e}"}, status=status.HTTP_400_BAD_REQUEST)


class ThreadsProfile(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    def get(self, request):
        auth_token = request.headers.get('Authorization')
        brand_id = request.headers.get('brand')
        user_id = decode_token(auth_token)
        try:
            profile_data = {}
            third_party_auth = ThirdPartyAuth.objects.get(
                brand_id=brand_id)
            if third_party_auth.thread_check:
                threads_data = get_threads_user_profile_info(
                    third_party_auth.thread_auth_token, third_party_auth.thread_user_id)
                username = threads_data.get('username')
                name = threads_data.get('name')
                profile_picture_url = threads_data.get('profile_picture_url')
                media = threads_data.get('media')
                profile_data["data"] = {
                    "username": username,
                    "name": name,
                    "followers_count": '',
                    "follows_count": '',
                    "profile_image": profile_picture_url,
                    "post": {
                        'likes': '',
                        'comment': '',
                        'media_data': media
                    }
                }

            return Response({"status": True, "data": profile_data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"status": False, "message": f"Error: {e}"}, status=status.HTTP_400_BAD_REQUEST)


class UploadYoutubeVideo(APIView):
    @sentry_sdk.trace
    def post(self, request):
        video = request.FILES.get('video')
        if video:
            # Define media path
            # media_path = os.path.join(settings.MEDIA_ROOT, 'videos', video.name)
            relative_media_path = os.path.join('media', 'videos', video.name)
            absolute_media_path = os.path.join(
                settings.MEDIA_ROOT, 'videos', video.name)
            # Save file to media directory
            path = default_storage.save(absolute_media_path, video)
            file_url = os.path.join(settings.MEDIA_URL, 'videos', video.name)
            print(relative_media_path)
            upload = upload_video(
                creds='youtube/yooii', file=f'{absolute_media_path}', title="Ram Ram Test", description="Ram Dhun")

            return Response({"message": "Video uploaded successfully", "file_url": file_url, 'upload': upload})
        else:
            return Response({"error": "No video provided"}, status=400)


class HastagPostView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    pagination_class = CustomPagination
    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            hashtag_id = request.query_params.get('hashtag_id')
            user_id = decode_token(auth_token)
            hiden_post = HidePost.objects.filter(
                user_id=user_id).values_list('post_id', flat=True)
            blocked_user = Block.objects.filter(
                Q(from_user_id=user_id), Q(is_block=True)).values_list('to_user_id', flat=True)
            hashtag_posts = HashTags.objects.get(pk=hashtag_id)
            post_ids = hashtag_posts.posts if hashtag_posts.posts else []
            print(post_ids)
            post_data = Post.objects.filter(is_deleted=False, pk__in=post_ids).exclude(
                is_private=True).exclude(id__in=hiden_post).exclude(is_posted=False).exclude(user_id__in=blocked_user).order_by('-created_at')
            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''
            paginator = self.pagination_class()
            all_data = []
            for data in post_data:
                id = data.pk
                title = data.title
                des = data.description
                location = data.location
                likes = LikePost.objects.filter(post_id=data.pk).count()
                dislikes = data.dislikes
                comment_count = data.comments_count
                tagged = data.tagged_in
                created_at = data.created_at
                files = [f'{header}{domain}{file.file.url}' for file in PostFiles.objects.filter(
                    Q(post_id=data.pk))]
                width, height = get_image_dimensions(files[0]) if files else (0, 0)
                post_user_id = data.user.pk
                username = data.user.username
                name = data.user.name
                profile_image = f'{header}{domain}{data.user.profile_picture.url}' if data.user.profile_picture else ''
                is_liked = False
                latest_comment = ''
                try:
                    comment = Comment.objects.filter(
                        post=data).order_by('-pk').first()
                    if comment is not None:
                        latest_comment = f'{comment.user.username} {comment.comment_text}'
                    else:
                        latest_comment = ''

                except Comment.DoesNotExist:
                    latest_comment = ''
                try:
                    LikePost.objects.get(
                        Q(post_id=data.pk), Q(user_id=user_id))
                    is_liked = True
                except:
                    is_liked = False
                try:
                    SavedPost.objects.get(
                        Q(post_id=data.pk), Q(user_id=user_id))
                    is_saved = True
                except:
                    is_saved = False
                thumbail_files = []
                post_files = PostFiles.objects.filter(Q(post=data), Q(
                    is_video=True)).prefetch_related('postfilesthumbnail_set')
                for file in post_files:
                    thumbnails = file.postfilesthumbnail_set.all()
                    thumbail_files.extend(
                        [f'{header}{domain}{thumbnail.file.url}' for thumbnail in thumbnails])
                append_object = {
                    'id': id,
                    'title': title,
                    'description': des,
                    'location': location,
                    'likes': likes,
                    'dislikes': dislikes,
                    'comments_count': comment_count,
                    'tagged_in': tagged,
                    'created_at': created_at,
                    'files': files,
                    'width':width,
                    'height': height,
                    'thumbail_files': thumbail_files,
                    'latest_comment': latest_comment,
                    'user': {
                        'user_id': post_user_id,
                        'username': username,
                        'name': name,
                        'profile_image': profile_image
                    },
                    'is_liked': is_liked,
                    'is_saved': is_saved,
                }
                all_data.append(append_object)
            result_page = paginator.paginate_queryset(all_data, request)
            return paginator.get_paginated_response({'status': True, 'message': 'Data Found Successfully', 'data': result_page})
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


# Tagged Post Api
class TaggedUserPostView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    pagination_class = CustomPagination
    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            query_user_id = request.query_params.get('user_id')
            token_user_id = decode_token(auth_token)
            paginator = self.pagination_class()
            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''
            if query_user_id == None:
                user_id = decode_token(auth_token)
            else:
                user_id = query_user_id
            try:
                user = UserRegistration.objects.get(pk=user_id)
            except UserRegistration.DoesNotExist:
                return Response({'status': False, 'message': 'User Not Found'}, status=status.HTTP_400_BAD_REQUEST)
            request_platform = request.query_params.get('request_platform')

            blocked_users = Block.objects.filter(from_user_id=user_id).values_list('to_user_id',flat=True)
            from_blocked_users = Block.objects.filter(to_user_id=user_id).values_list('from_user_id',flat=True)

            if request_platform == '1' or request_platform is None:
                post_data = Post.objects.filter(Q(tagged_in__contains=[token_user_id])
                                                ).exclude(is_deleted=True).exclude(is_text_post=True).exclude(user_id__in=blocked_users).exclude(user_id__in=from_blocked_users).exclude(is_posted=False).order_by('-created_at')
                all_data = []
                for data in post_data:
                    id = data.pk
                    title = data.title
                    des = data.description
                    location = data.location
                    likes = LikePost.objects.filter(post_id=data.pk).count()
                    dislikes = data.dislikes
                    comment_count = data.comments_count
                    tagged = data.tagged_in
                    created_at = data.created_at
                    files = [f'{header}{domain}{file.file.url}' for file in PostFiles.objects.filter(
                        Q(post_id=data.pk))]
                    width, height = get_image_dimensions(files[0]) if files else (0, 0)
                    post_user_id = data.user.pk
                    username = data.user.username
                    name = data.user.name
                    profile_image = f'{header}{domain}{data.user.profile_picture.url}' if data.user.profile_picture else ''
                    is_liked = False
                    latest_comment = ''
                    try:
                        comment = Comment.objects.filter(
                            post=data).order_by('-pk').first()
                        if comment is not None:
                            latest_comment = f'{comment.user.username} {comment.comment_text}'
                        else:
                            latest_comment = ''

                    except Comment.DoesNotExist:
                        latest_comment = ''
                    try:
                        LikePost.objects.get(
                            Q(post_id=data.pk), Q(user_id=token_user_id))
                        is_liked = True
                    except:
                        is_liked = False
                    try:
                        SavedPost.objects.get(
                            Q(post_id=data.pk), Q(user_id=user_id))
                        is_saved = True
                    except:
                        is_saved = False
                    append_object = {
                        'id': id,
                        'title': title,
                        'description': des,
                        'location': location,
                        'likes': likes,
                        'dislikes': dislikes,
                        'comments_count': comment_count,
                        'tagged_in': tagged,
                        'created_at': created_at,
                        'files': files,
                        'width':width,
                        'height': height,
                        'latest_comment': latest_comment,
                        'user': {
                            'user_id': post_user_id,
                            'username': username,
                            'name': name,
                            'profile_image': profile_image
                        },
                        'is_liked': is_liked,
                        'is_saved': is_saved,
                    }
                    all_data.append(append_object)

                result_page = paginator.paginate_queryset(all_data, request)
                return paginator.get_paginated_response({'status': True, 'message': 'Data Found Successfully', 'data': result_page})
            elif request_platform == '2':
                data = Post.objects.filter(
                    user_id=user_id).order_by('-created_at').exclude(is_deleted=True).exclude(user_id__in=blocked_users).exclude(user_id__in=from_blocked_users).exclude(is_posted=False)
                result_page = paginator.paginate_queryset(data, request)
                post_serializer = PostViewAdminSerializer(
                    result_page, many=True, context={'request': request})

                return paginator.get_paginated_response({'status': True, 'message': 'Data Found Successfully', 'data': post_serializer.data})
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class GetSocialPlatforms(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    def get(self, request):
        auth_token = request.headers.get('Authorization')
        brand_id = request.headers.get('brand')
        user_id = decode_token(auth_token)

        brand = ThirdPartyAuth.objects.get(brand_id=brand_id)

        social_platforms = SocialPlatforms.objects.get(pk=1)



        social_object ={
            'facebook':social_platforms.facebook,
            'instagram': social_platforms.instagram,
            'thread':  social_platforms.threads,
            'linkedin':  social_platforms.linkedin,
            'pintrest': social_platforms.pinterest,
            'tumblr':  social_platforms.tumblr,
            'Reddit':  social_platforms.reddit,
            'Youtube': social_platforms.youtube,
            'tiktok':social_platforms.tiktok,
        }
        user_object = {
            'facebook':brand.facebook_check ,
            'instagram': brand.instagram_check, 
            'linkedin': brand.linkedin_check ,
            'pintrest': brand.pinterest_check ,
            'tumblr': brand.tumblr_check ,
            'Reddit': brand.reddit_check ,
            'Youtube': brand.youtube_check, 
            'tiktok': brand.tiktok_check ,
        }
        return Response({"status": True, "data": {"platform_status":social_object,"user_status":user_object}}, status=status.HTTP_200_OK)
    
    

class FeedbackView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    @sentry_sdk.trace
    def post(self, request):
        auth_token = request.headers.get('Authorization')
        user_id = decode_token(auth_token)
        brand_id = request.headers.get('brand')

        local_user_id = request.headers.get('user')
        role_status, role_permission_result = check_multiple_roles_permissions(local_user_id, brand_id)
        if role_status == False:
            return Response({'status': False, "message": 'You Do Not Have Permission'}, status=status.HTTP_400_BAD_REQUEST)
        if 7 not in role_permission_result :
            return Response({'status': False, "message": 'You Do Not Have Permission'}, status=status.HTTP_400_BAD_REQUEST)
        
        app_using_frequency = request.data.get('app_using_frequency')
        description = request.data.get('description')
        stars = request.data.get('stars')
        file = request.FILES.get('file')  
        if not app_using_frequency or not stars:
            return Response({'status': False, 'message': 'All Fields Are Required'}, status=status.HTTP_400_BAD_REQUEST)
        try:
            feedback = Feedback.objects.create(user_id=user_id, app_using_frequency=app_using_frequency,description=description,stars=stars,file=file)
            return Response({'status': True, 'message': 'Feedback Submitted Successfully'}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        

# CHAT



class GetDetailedMessagesView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            brand_id = request.headers.get('brand')
            platform = int(request.headers.get('platform'))  # 1 (Facebook) 2 (Instagram)
            convo_id = request.headers.get('conversation')
            owner_id = request.headers.get('owner')
            subscription_id = request.headers.get('subscription')
            user_id = decode_token(auth_token)
            # check_sub = check_user_plan(user_id, int(subscription_id))


            facebook_access =True
            instagram_access = True

            if not facebook_access and not instagram_access:
                return Response({
                    'status': False,
                    'is_subscription': False,
                    'message': 'Your subscription does not include access to any social messaging platforms'
                }, status=status.HTTP_400_BAD_REQUEST)

            try:
                data = None
                third_party_data = ThirdPartyAuth.objects.get(brand_id=brand_id)

                if platform == 1 and facebook_access and third_party_data.facebook_check:
                    facebook_status, message_data = get_facebook_messages(third_party_data.facebook_token, convo_id)
                    check_id = third_party_data.facebook_page_id
                    if facebook_status:
                        for message in message_data:
                            from_id = message["from"]["id"]
                            to_ids = message["to"]["data"][0]["id"]
                            print('to_ids', from_id)
                            message["check_message"] = 1 if from_id == check_id else 2
                        data = message_data
                        return Response({'status': True, 'message': 'Data Found Successfully', 'data': data[::-1]}, status=status.HTTP_200_OK)

                if platform == 2 and instagram_access and third_party_data.instagram_check:
                    instagram_status, message_data = get_full_conversation_list(third_party_data.insta_auth_token, convo_id)
                    if instagram_status:
                        for message in message_data:
                            from_id = message["from"]["id"]
                            to_ids = message["to"]["data"][0]["id"]
                            print('to_ids', from_id)
                            message["check_message"] = 1 if from_id == owner_id else 2
                        data = message_data
                        return Response({'status': True, 'message': 'Data Found Successfully', 'data': data}, status=status.HTTP_200_OK)

                return Response({'status': False, 'message': 'Error Fetching Message Data (Please Check Conversation Id)'}, status=status.HTTP_400_BAD_REQUEST)

            except ThirdPartyAuth.DoesNotExist:
                return Response({'status': False, 'message': 'Brand Not Found'}, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)



class SendInstagramMessageView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            brand_id = request.headers.get('brand')
            message = request.data.get('message')
            to_user_id = request.data.get('to_user_id')
            from_user_id = request.data.get('from_user_id')
            # is_video = request.data.get('is_video')
            file = request.FILES.get('file')
            message_type = int(request.data.get('message_type', 1))  # 1 (Text Message) 2 (Files/Images/GIF)
            subscription_id = request.headers.get('subscription')

            user_id = decode_token(auth_token)

            instagram_access = True

            if not instagram_access:
                return Response({
                    'status': False,
                    'is_subscription': False,
                    'message': 'Your subscription does not include access to any social platforms'
                }, status=status.HTTP_400_BAD_REQUEST)

            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''

            try:
                third_party_data = ThirdPartyAuth.objects.get(brand_id=brand_id)
                if third_party_data.instagram_check:
                    if message_type == 1:
                        insta_status, send_message = send_instagram_message(from_user_id, third_party_data.insta_auth_token, to_user_id, message)
                        print(send_message)
                        if insta_status:
                            return Response({'status': True, 'message': 'Message Sent Successfully', 'data': send_message}, status=status.HTTP_200_OK)
                        else:
                            return Response({'status': False, 'message': 'Error While Sending Message'}, status=status.HTTP_400_BAD_REQUEST)
                    if message_type == 2:
                        file_name = str(file)
                        temp_dir = os.path.join(settings.MEDIA_ROOT, "tmp")
                        try:
                            os.makedirs(temp_dir, exist_ok=True)
                        except OSError as e:
                            print(f"Error creating directory {temp_dir}: {e}")
                            temp_dir = "/tmp"

                        temp_file_path = os.path.join(temp_dir, file_name)

                        with open(temp_file_path, 'wb+') as temp_file:
                            for chunk in file.chunks():
                                temp_file.write(chunk)

                        image_url = f'{header}{domain}/media/tmp/{file_name}'
                        media_type = "image"
                        insta_status, send_message = send_instagram_images_message(from_user_id, to_user_id, image_url, media_type, third_party_data.insta_auth_token)
                        print(send_message)
                        if insta_status:
                            if os.path.exists(temp_file_path):
                                os.remove(temp_file_path)
                            return Response({'status': True, 'message': 'Message Sent Successfully', 'data': send_message}, status=status.HTTP_200_OK)
                        else:
                            return Response({'status': False, 'message': 'Error While Sending Message'}, status=status.HTTP_400_BAD_REQUEST)
                else:
                    return Response({'status': False, 'message': 'Please Connect Instagram For Messaging Services'}, status=status.HTTP_400_BAD_REQUEST)

            except ThirdPartyAuth.DoesNotExist:
                return Response({'status': False, 'message': 'Please Connect Instagram For Messaging Services'}, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)



class SendFacebookMessageView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def post(self, request):
        try:
            # Retrieve headers and data
            auth_token = request.headers.get('Authorization')
            brand_id = request.headers.get('brand')
            message = request.data.get('message')
            to_user_id = request.data.get('to_user_id')
            from_user_id = request.data.get('from_user_id')
            file = request.FILES.get('file')
            message_type = int(request.data.get('message_type', 1))  # 1 (Text Message) 2 (Files/Images/GIF)
            subscription_id = request.headers.get('subscription')

            user_id = decode_token(auth_token)

            facebook_access = True

            if not facebook_access:
                return Response({
                    'status': False,
                    'is_subscription': False,
                    'message': 'Your subscription does not include access to any social platforms'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''

            try:
                third_party_data = ThirdPartyAuth.objects.get(brand_id=brand_id)
                if third_party_data.facebook_check:
                    if message_type == 1:
                        facebook_status, send_message = send_facebook_message(
                            third_party_data.facebook_page_id,
                            third_party_data.facebook_token,
                            to_user_id,
                            message
                        )
                        print(send_message)
                        if facebook_status:
                            return Response({'status': True, 'message': 'Message Sent Successfully', 'data': send_message}, status=status.HTTP_200_OK)
                        else:
                            return Response({'status': False, 'message': 'Error While Sending Message'}, status=status.HTTP_400_BAD_REQUEST)

                    if message_type == 2:
                        file_name = str(file)
                        temp_dir = os.path.join(settings.MEDIA_ROOT, "tmp")
                        try:
                            os.makedirs(temp_dir, exist_ok=True)
                        except OSError as e:
                            print(f"Error creating directory {temp_dir}: {e}")
                            temp_dir = "/tmp"

                        temp_file_path = os.path.join(temp_dir, file_name)

                        with open(temp_file_path, 'wb+') as temp_file:
                            for chunk in file.chunks():
                                temp_file.write(chunk)

                        image_url = f'{header}{domain}/media/tmp/{file_name}'
                        media_type = "image"
                        facebook_status, send_message = send_facebook_media_message(
                            third_party_data.facebook_token,
                            to_user_id,
                            image_url
                        )
                        print(send_message)
                        if facebook_status:
                            if os.path.exists(temp_file_path):
                                os.remove(temp_file_path)
                            return Response({'status': True, 'message': 'Message Sent Successfully', 'data': send_message}, status=status.HTTP_200_OK)
                        else:
                            return Response({'status': False, 'message': 'Error While Sending Message'}, status=status.HTTP_400_BAD_REQUEST)

                else:
                    return Response({'status': False, 'message': 'Please Connect Facebook For Messaging Services'}, status=status.HTTP_400_BAD_REQUEST)

            except ThirdPartyAuth.DoesNotExist:
                return Response({'status': False, 'message': 'Please Connect Facebook For Messaging Services'}, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        
class LoadDataView(APIView):
    @sentry_sdk.trace
    def get(self,request):
        try:
            countries_file='helpers/countries.csv'
            states_file='helpers/states.csv'
            cities_file='helpers/cities.csv'
            category_file='helpers/category.csv'
            post_industry='helpers/industries.csv'
            response = process_and_store_data(countries_file,states_file,cities_file,category_file,post_industry)
            return Response({'status':True,'message':response},status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

class ListCountryCityStateView(APIView):
    @sentry_sdk.trace
    def get(self,request):
        try:
            request_type = int(request.query_params.get('request_type')) # 1 (Country) 2 (State) 3 (City)
            forwarding_id = str(request.query_params.get('forwarding_id'))

            response_data = []
            data_key = ""
            try:
                if request_type != 1 and not forwarding_id:
                    return Response({'status':False,'message':'For request_type 2 & 3 forwarding_id is required'},status=status.HTTP_400_BAD_REQUEST)
                if request_type == 1:
                    all_data = Country.objects.all()
                    for data in all_data:    
                        append_obj = {
                            "request_type":request_type,
                            "forwarding_id":int(data.country_id),
                            "name":data.name
                        }
                        response_data.append(append_obj)
                if request_type == 2:
                    all_data = State.objects.filter(country_id=forwarding_id)
                    for data in all_data:    
                        append_obj = {
                            "request_type":request_type,
                            "forwarding_id":int(data.state_id),
                            "name":data.name
                        }
                        response_data.append(append_obj)
                
                if request_type  == 3:
                    all_data = City.objects.filter(state_id=forwarding_id)
                    for data in all_data:    
                        append_obj = {
                            "request_type":request_type,
                            "forwarding_id":int(data.city_id),
                            "name":data.name
                        }
                        response_data.append(append_obj)
                
                return Response({'status':True,'message':'Data Found Successfully','data':response_data},status=status.HTTP_200_OK)
            except (Country.DoesNotExist,State.DoesNotExist,City.DoesNotExist):
                return Response({'status':False,'message':'Data Not Found Please Check The Request'},status=status.HTTP_400_BAD_REQUEST)
        
        except Exception as e :
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        



class SearchLocationView(APIView):
    @sentry_sdk.trace
    def get(self,request):
        try:
            search_text = request.query_params.get('search_text')
            lat = request.query_params.get('lat')
            long = request.query_params.get('long')

            if search_text:
                response_status,data = search_places(search_text)
            if lat and long:
                response_status,data = nearby_places(lat,long)

            if response_status:
                return Response({'status':True,'message':'Data Found Successfully','data':data},status=status.HTTP_200_OK)
            else:
                return Response({'status':True,'message':'Data Not Found','data':data},status=status.HTTP_400_BAD_REQUEST)
        except Exception as e :
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

class ThirdPartyChatListMessageView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def get(self, request):
        auth_token = request.headers.get('Authorization')
        brand_id = request.headers.get('brand')
        user_id = decode_token(auth_token)
        facebook_access = True
        instagram_access = True
        try:
            all_messages = {
                'facebook': [],
                'instagram': []
            }
            third_party_data = ThirdPartyAuth.objects.get(brand_id=brand_id)
            
            if facebook_access and third_party_data.facebook_check:
                facebook_list = get_facebook_conversations(
                    third_party_data.facebook_token,
                    third_party_data.facebook_page_id
                )
                for facebook_data in facebook_list:
                    append_data = {
                        'name': facebook_data['participants'][0]['name'],
                        'conversation_id': facebook_data['conversation_id'],
                        'owner_id': third_party_data.facebook_page_id,
                        'chatting_user_id': facebook_data['participants'][0]['psid'],
                        'last_message': facebook_data['messages'][0]['message'] if facebook_data['messages'] else ''
                    }
                    all_messages['facebook'].append(append_data)
            
            if instagram_access and third_party_data.instagram_check:
                owner_id, instagram_list = get_conversation_list(third_party_data.insta_auth_token)
                for instagram_data in instagram_list:
                    append_data = {
                        'name': instagram_data['participants'][0]['username'],
                        'conversation_id': instagram_data['conversation_id'],
                        'owner_id': owner_id,
                        'chatting_user_id': instagram_data['participants'][0]['id'],
                        'last_message': instagram_data['messages'][0]['message'] if instagram_data['messages'] else ''
                    }
                    all_messages['instagram'].append(append_data)           
                
            return Response({'status': True, 'message': 'Data Found Successfully', 'data': all_messages}, status=status.HTTP_200_OK)
            
        except ThirdPartyAuth.DoesNotExist:
            return Response({'status': False, 'message': 'Brand not found or not connected to any platforms'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)



class GetDetailedMessagesView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            brand_id = request.headers.get('brand')
            platform = int(request.headers.get('platform'))  # 1 (Facebook) 2 (Instagram)
            convo_id = request.headers.get('conversation')
            owner_id = request.headers.get('owner')
            subscription_id = request.headers.get('subscription')
            user_id = decode_token(auth_token)
            # check_sub = check_user_plan(user_id, int(subscription_id))


            facebook_access =True
            instagram_access = True

            if not facebook_access and not instagram_access:
                return Response({
                    'status': False,
                    'is_subscription': False,
                    'message': 'Your subscription does not include access to any social messaging platforms'
                }, status=status.HTTP_400_BAD_REQUEST)

            try:
                data = None
                third_party_data = ThirdPartyAuth.objects.get(brand_id=brand_id)

                if platform == 1 and facebook_access and third_party_data.facebook_check:
                    facebook_status, message_data = get_facebook_messages(third_party_data.facebook_token, convo_id)
                    check_id = third_party_data.facebook_page_id
                    if facebook_status:
                        for message in message_data:
                            from_id = message["from"]["id"]
                            to_ids = message["to"]["data"][0]["id"]
                            print('to_ids', from_id)
                            message["check_message"] = 1 if from_id == check_id else 2
                        data = message_data
                        return Response({'status': True, 'message': 'Data Found Successfully', 'data': data[::-1]}, status=status.HTTP_200_OK)

                if platform == 2 and instagram_access and third_party_data.instagram_check:
                    instagram_status, message_data = get_full_conversation_list(third_party_data.insta_auth_token, convo_id)
                    if instagram_status:
                        for message in message_data:
                            from_id = message["from"]["id"]
                            to_ids = message["to"]["data"][0]["id"]
                            print('to_ids', from_id)
                            message["check_message"] = 1 if from_id == owner_id else 2
                        data = message_data
                        return Response({'status': True, 'message': 'Data Found Successfully', 'data': data}, status=status.HTTP_200_OK)

                return Response({'status': False, 'message': 'Error Fetching Message Data (Please Check Conversation Id)'}, status=status.HTTP_400_BAD_REQUEST)

            except ThirdPartyAuth.DoesNotExist:
                return Response({'status': False, 'message': 'Brand Not Found'}, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)



class SendInstagramMessageView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            brand_id = request.headers.get('brand')
            message = request.data.get('message')
            to_user_id = request.data.get('to_user_id')
            from_user_id = request.data.get('from_user_id')
            # is_video = request.data.get('is_video')
            file = request.FILES.get('file')
            message_type = int(request.data.get('message_type', 1))  # 1 (Text Message) 2 (Files/Images/GIF)
            subscription_id = request.headers.get('subscription')

            user_id = decode_token(auth_token)

            instagram_access = True

            if not instagram_access:
                return Response({
                    'status': False,
                    'is_subscription': False,
                    'message': 'Your subscription does not include access to any social platforms'
                }, status=status.HTTP_400_BAD_REQUEST)

            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''

            try:
                third_party_data = ThirdPartyAuth.objects.get(brand_id=brand_id)
                if third_party_data.instagram_check:
                    if message_type == 1:
                        insta_status, send_message = send_instagram_message(from_user_id, third_party_data.insta_auth_token, to_user_id, message)
                        print(send_message)
                        if insta_status:
                            return Response({'status': True, 'message': 'Message Sent Successfully', 'data': send_message}, status=status.HTTP_200_OK)
                        else:
                            return Response({'status': False, 'message': 'Error While Sending Message'}, status=status.HTTP_400_BAD_REQUEST)
                    if message_type == 2:
                        file_name = str(file)
                        temp_dir = os.path.join(settings.MEDIA_ROOT, "tmp")
                        try:
                            os.makedirs(temp_dir, exist_ok=True)
                        except OSError as e:
                            print(f"Error creating directory {temp_dir}: {e}")
                            temp_dir = "/tmp"

                        temp_file_path = os.path.join(temp_dir, file_name)

                        with open(temp_file_path, 'wb+') as temp_file:
                            for chunk in file.chunks():
                                temp_file.write(chunk)

                        image_url = f'{header}{domain}/media/tmp/{file_name}'
                        media_type = "image"
                        insta_status, send_message = send_instagram_images_message(from_user_id, to_user_id, image_url, media_type, third_party_data.insta_auth_token)
                        print(send_message)
                        if insta_status:
                            if os.path.exists(temp_file_path):
                                os.remove(temp_file_path)
                            return Response({'status': True, 'message': 'Message Sent Successfully', 'data': send_message}, status=status.HTTP_200_OK)
                        else:
                            return Response({'status': False, 'message': 'Error While Sending Message'}, status=status.HTTP_400_BAD_REQUEST)
                else:
                    return Response({'status': False, 'message': 'Please Connect Instagram For Messaging Services'}, status=status.HTTP_400_BAD_REQUEST)

            except ThirdPartyAuth.DoesNotExist:
                return Response({'status': False, 'message': 'Please Connect Instagram For Messaging Services'}, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)



class SendFacebookMessageView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def post(self, request):
        try:
            # Retrieve headers and data
            auth_token = request.headers.get('Authorization')
            brand_id = request.headers.get('brand')
            message = request.data.get('message')
            to_user_id = request.data.get('to_user_id')
            from_user_id = request.data.get('from_user_id')
            file = request.FILES.get('file')
            message_type = int(request.data.get('message_type', 1))  # 1 (Text Message) 2 (Files/Images/GIF)
            subscription_id = request.headers.get('subscription')

            user_id = decode_token(auth_token)

            facebook_access = True

            if not facebook_access:
                return Response({
                    'status': False,
                    'is_subscription': False,
                    'message': 'Your subscription does not include access to any social platforms'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''

            try:
                third_party_data = ThirdPartyAuth.objects.get(brand_id=brand_id)
                if third_party_data.facebook_check:
                    if message_type == 1:
                        facebook_status, send_message = send_facebook_message(
                            third_party_data.facebook_page_id,
                            third_party_data.facebook_token,
                            to_user_id,
                            message
                        )
                        print(send_message)
                        if facebook_status:
                            return Response({'status': True, 'message': 'Message Sent Successfully', 'data': send_message}, status=status.HTTP_200_OK)
                        else:
                            return Response({'status': False, 'message': 'Error While Sending Message'}, status=status.HTTP_400_BAD_REQUEST)

                    if message_type == 2:
                        file_name = str(file)
                        temp_dir = os.path.join(settings.MEDIA_ROOT, "tmp")
                        try:
                            os.makedirs(temp_dir, exist_ok=True)
                        except OSError as e:
                            print(f"Error creating directory {temp_dir}: {e}")
                            temp_dir = "/tmp"

                        temp_file_path = os.path.join(temp_dir, file_name)

                        with open(temp_file_path, 'wb+') as temp_file:
                            for chunk in file.chunks():
                                temp_file.write(chunk)

                        image_url = f'{header}{domain}/media/tmp/{file_name}'
                        media_type = "image"
                        facebook_status, send_message = send_facebook_media_message(
                            third_party_data.facebook_token,
                            to_user_id,
                            image_url
                        )
                        print(send_message)
                        if facebook_status:
                            if os.path.exists(temp_file_path):
                                os.remove(temp_file_path)
                            return Response({'status': True, 'message': 'Message Sent Successfully', 'data': send_message}, status=status.HTTP_200_OK)
                        else:
                            return Response({'status': False, 'message': 'Error While Sending Message'}, status=status.HTTP_400_BAD_REQUEST)

                else:
                    return Response({'status': False, 'message': 'Please Connect Facebook For Messaging Services'}, status=status.HTTP_400_BAD_REQUEST)

            except ThirdPartyAuth.DoesNotExist:
                return Response({'status': False, 'message': 'Please Connect Facebook For Messaging Services'}, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        
class IndustryListView(APIView):
    @sentry_sdk.trace
    def get(self, request):
        try:
            industries = PostIndustry.objects.all().order_by('name')
            
            industries_data = []
            for industry in industries:
                industries_data.append({
                    'id': industry.id,
                    'name': industry.name
                })
            return Response({'status': True,'message': 'Industries retrieved successfully','data': industries_data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        

class LinkedInAnalyticsViewGraphOne(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            brand_id = request.headers.get("brand")
            subscription_id = request.headers.get('subscription')
            user_id = decode_token(auth_token)

            local_user_id = request.headers.get('user')
            role_status, role_permission_result = check_multiple_roles_permissions(local_user_id, brand_id)
            if role_status == False:
                print("role_status",role_status)
                return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
            if 3 not in role_permission_result :
                print("role_permission_result",role_permission_result)
                return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
            

            check_sub = check_user_plan(user_id, int(subscription_id))
            print("check_sub", check_sub)

            linkedin_access = check_sub['social_platforms'].get('linkedin', False)

            if not linkedin_access:
                return Response({
                    'status': False,
                    'is_subscription': False,
                    'message': 'Your subscription does not include access to any social platforms'
                }, status=status.HTTP_400_BAD_REQUEST)

            third_party = ThirdPartyAuth.objects.get(brand_id=brand_id)
            start_date = request.data.get('start_date')
            end_date = request.data.get('end_date')
            current_page_id = ''
            linkedin_access_token = ''
            final_delivery_data = {}

            if third_party.linkedin_check:
                linkedin_access_token = third_party.linked_in_token
                page_id_status, page_ids = get_linkedin_organization_id(linkedin_access_token)

                if page_id_status and page_ids:
                    current_page_id = page_ids[0]
                else:
                    return Response({'status': False, 'message': 'You Do Not Have A Professional Company Page Associated With Your Account'}, status=status.HTTP_400_BAD_REQUEST)

                follower_status, follower_data = get_linkedin_follower_stats(
                    current_page_id, start_date, end_date, linkedin_access_token
                )

                print(current_page_id)

                followers_in_range = 0
                posts_in_range = 0
                impressions_in_range = 0
                days_count = 0
                if follower_status:
                    graph_data = [] 
                    for follow_element in follower_data.get("data", {}).get("elements", []):
                        start_timestamp = follow_element.get("timeRange", {}).get("start")

                        if start_timestamp:
                            formatted_date = datetime.utcfromtimestamp(start_timestamp / 1000).strftime('%d-%m-%Y')

                            organic_followers = follow_element.get("followerGains", {}).get("organicFollowerGain", 0)
                            followers_in_range += organic_followers
                            days_count += 1
                            graph_data.append({formatted_date: organic_followers})

                    final_delivery_data['followers'] = {
                        "total_followers": follower_data.get("total_followers", 0),
                        "graph_data": graph_data
                    }

                posts_status, posts_data = linkedin_total_posts_count(linkedin_access_token, current_page_id, start_date, end_date)
                if posts_status:
                    final_delivery_data['posts'] = posts_data
                    posts_in_range = posts_data['total']

                impression_status, impression_data = get_linkedin_date_impression(extract_organization_id(current_page_id), start_date, end_date, linkedin_access_token)
                print("impression_status", impression_status)
                if impression_status:
                    total_impression_count = 0
                    impression_graph_data = []
                    for impression_element in impression_data.get("data", {}).get("elements", []):
                        start_timestamp = impression_element.get("timeRange", {}).get("start")

                        if start_timestamp:
                            formatted_date = datetime.utcfromtimestamp(start_timestamp / 1000).strftime('%d-%m-%Y')

                            impressions = impression_element.get("totalShareStatistics", {}).get("impressionCount", 0)
                            impressions_in_range += impressions
                            total_impression_count += impressions
                            impression_graph_data.append({formatted_date: impressions})

                    final_delivery_data['impressions'] = {
                        "total_impressions": total_impression_count,
                        "graph_data": impression_graph_data
                    }

                final_delivery_data['cards'] = {
                    'followers': followers_in_range,
                    'daily_followers': followers_in_range / days_count,
                    'followers_per_post': followers_in_range / posts_in_range,
                    'daily_impressions': impressions_in_range / days_count,
                    'daily_posts': posts_in_range / days_count
                }

                return Response({'status': True, 'message': 'Data Found Successfully', 'data': final_delivery_data}, status=status.HTTP_200_OK)

            else:
                return Response({'status': False, 'message': 'Please Connect To Linkedin'}, status=status.HTTP_400_BAD_REQUEST)

        except ThirdPartyAuth.DoesNotExist:
            return Response({'status': False, 'message': 'Please Register The Brand'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class LinkedInAnalyticsViewGraphtwo(APIView):
    authentication_classes = [CustomJWTAuthentication]
    def post(self,request):
        try:
            brand_id = request.headers.get("brand")
            auth_token = request.headers.get('Authorization')
            subscription_id = request.headers.get('subscription')
            user_id = decode_token(auth_token)


            local_user_id = request.headers.get('user')
            role_status, role_permission_result = check_multiple_roles_permissions(local_user_id, brand_id)
            if role_status == False:
                return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
            if 3 not in role_permission_result :
                return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
            

            check_sub = check_user_plan(user_id, int(subscription_id))
            print("check_sub", check_sub)

            linkedin_access = check_sub['social_platforms'].get('linkedin', False)

            if not linkedin_access:
                return Response({
                    'status': False,
                    'is_subscription': False,
                    'message': 'Your subscription does not include access to any social platforms'
                }, status=status.HTTP_400_BAD_REQUEST)

            third_party = ThirdPartyAuth.objects.get(brand_id=brand_id)
            start_date = request.data.get('start_date')
            end_date = request.data.get('end_date')
            current_page_id = ''
            linkedin_access_token = ''
            final_delivery_data = {}

            if third_party.linkedin_check:
                linkedin_access_token = third_party.linked_in_token
                page_id_status, page_ids = get_linkedin_organization_id(linkedin_access_token)

                if page_id_status and page_ids:
                    current_page_id = page_ids[0]
                else:
                    return Response({'status':False,'message':'You Do Not Have A Professional Company Page Associated With Your Account'},status=status.HTTP_400_BAD_REQUEST)

                follower_status, follower_data = get_linkedin_follower_statistics(
                    current_page_id, linkedin_access_token
                )

                if follower_status:
                    graph_data = {
                        "country":[],
                        "area":[],
                        "seniority":[],
                        "industry":[],
                        "function":[],
                        "company_size":[]
                    }
                    follow_element =  follower_data.get("data", {}).get("elements", [])[0]
                    total_followers = follower_data.get("total",0)
                    followers_by_country = follow_element.get("followerCountsByGeoCountry",[])
                    followers_by_area = follow_element.get("followerCountsByGeo",[])
                    followers_by_seniority = follow_element.get("followerCountsBySeniority",[])
                    followers_by_industry = follow_element.get("followerCountsByIndustry",[])
                    followers_by_functions = follow_element.get("followerCountsByFunction",[])
                    followers_by_company_size = follow_element.get("followerCountsByStaffCountRange",[])
                    for follow_country in followers_by_country:
                        country_status,country_name = get_country_by_id(linkedin_access_token,extract_organization_id(follow_country['geo']))
                        if country_status:
                            country_value = follow_country['followerCounts']['organicFollowerCount']
                            persantage_value = country_value / total_followers * 100
                        
                        graph_data['country'].append({
                                 "country_name":country_name,
                                "persantage_value":persantage_value
                            }
                        )
                    
                    for follow_area in followers_by_area:
                        area_status,area_name = get_country_by_id(linkedin_access_token,extract_organization_id(follow_area['geo']))
                        if area_status:
                            area_value = follow_area['followerCounts']['organicFollowerCount']
                            persantage_value = area_value / total_followers * 100
                        
                        graph_data['area'].append({
                                "area_name":area_name,
                                "persantage_value":persantage_value
                        })

                    for follow_seniority in followers_by_seniority:
                        seniority_status,seniority_name = get_seniority_by_id(linkedin_access_token,extract_organization_id(follow_seniority['seniority']))
                        if seniority_status:
                            seniority_value = follow_seniority['followerCounts']['organicFollowerCount']
                            persantage_value = seniority_value / total_followers * 100
                        
                        graph_data['seniority'].append({
                                "seniority_name":seniority_name,
                                "persantage_value":persantage_value
                        })
                    
                    for follow_industry in followers_by_industry:
                        industry_status,industry_name = get_industry_by_id(linkedin_access_token,extract_organization_id(follow_industry['industry']))
                        if industry_status:
                            industry_value = follow_industry['followerCounts']['organicFollowerCount']
                            persantage_value = industry_value / total_followers * 100
                        
                        graph_data['industry'].append({
                                "industry_name":industry_name,
                                "persantage_value":persantage_value
                        })
                    
                    FUNCTIONS_JSON_PATH = 'linkedin/linkedin_functions_cache.json'
                    if os.path.exists(FUNCTIONS_JSON_PATH):
                        with open(FUNCTIONS_JSON_PATH, 'r') as f:
                            function_cache = json.load(f)
                    else:
                        function_cache = {}
                    for follow_functions in followers_by_functions:
                        function_id = extract_organization_id(follow_functions['function'])
                        print("Function ID:", function_id)

                        function_name = function_cache.get(str(function_id)) 

                        if not function_name:
                            function_status, function_name = get_function_by_id(linkedin_access_token, function_id)
                            if function_status:
                                function_cache[str(function_id)] = function_name
                                with open(FUNCTIONS_JSON_PATH, 'w') as f:
                                    json.dump(function_cache, f, indent=4)

                        if function_name:
                            industry_value = follow_functions['followerCounts']['organicFollowerCount']
                            persantage_value = industry_value / total_followers * 100

                            graph_data['function'].append({
                                "function_name": function_name,
                                "persantage_value": persantage_value
                            })



                    for follow_company_size in followers_by_company_size:
                        company_size_name = follow_company_size['staffCountRange']
                        company_size_value = follow_company_size['followerCounts']['organicFollowerCount']
                        persantage_value = company_size_value / total_followers * 100
                        
                        graph_data['company_size'].append({
                                "function_name":company_size_name,
                                "persantage_value":persantage_value
                        })

                    final_delivery_data['followers'] = {
                            "total_followers": follower_data.get("total", 0),
                            "graph_data": graph_data
                    }
                    return Response({'status':True,'message':'Data Found Successfully','data':final_delivery_data},status=status.HTTP_200_OK)
                else:
                    return Response({'status':False,'message':'There was an issue fetching data'},status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        


class LinkedInAnalyticsViewGraphthree(APIView):
    authentication_classes = [CustomJWTAuthentication]
    
    def post(self, request):
        try:
            brand_id = request.headers.get("brand")
            subscription_id = request.headers.get('subscription')
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            
            local_user_id = request.headers.get('user')
            role_status, role_permission_result = check_multiple_roles_permissions(local_user_id, brand_id)
            if role_status == False:
                return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
            if 3 not in role_permission_result :
                return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
            
            check_sub = check_user_plan(user_id, int(subscription_id))
            print("check_sub", check_sub)

            linkedin_access = check_sub['social_platforms'].get('linkedin', False)

            if not linkedin_access:
                return Response({
                    'status': False,
                    'is_subscription': False,
                    'message': 'Your subscription does not include access to any social platforms'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            third_party = ThirdPartyAuth.objects.get(brand_id=brand_id)
            start_date = request.data.get('start_date')
            end_date = request.data.get('end_date')
            current_page_id = ''
            linkedin_access_token = ''
            final_delivery_data = {}

            if third_party.linkedin_check:
                linkedin_access_token = third_party.linked_in_token
                page_id_status, page_ids = get_linkedin_organization_id(linkedin_access_token)

                if page_id_status and page_ids:
                    current_page_id = page_ids[0]
                else:
                    return Response({'status': False, 'message': 'You Do Not Have A Professional Company Page Associated With Your Account'}, status=status.HTTP_400_BAD_REQUEST)

                share_status, share_data = get_linkedin_share_statistics(
                    extract_organization_id(current_page_id), start_date, end_date, linkedin_access_token
                )

                engagements_in_range = 0
                unique_impressions_in_range = 0
                share_count_in_range = 0
                click_count_in_range = 0
                like_count_in_range = 0
                impression_count_in_range = 0
                comment_count_in_range = 0
                days_count = 0
                posts_in_range = 0
                if share_status:
                    posts_status, posts_data = linkedin_total_posts_count(linkedin_access_token, current_page_id, start_date, end_date)
                    if posts_status:
                        posts_in_range = posts_data['total']
                    graph_data = {
                        "engagement": [],
                        "unique_impressions": [],
                        "share_count": [],
                        "click_count": [],
                        "like_count": [],
                        "impression_count": [],
                        "comment_count": []
                    }

                    for share_element_engagement in share_data.get("data", {}).get("elements", []):
                        start_timestamp = share_element_engagement.get("timeRange", {}).get("start")

                        if start_timestamp:
                            formatted_date = datetime.utcfromtimestamp(start_timestamp / 1000).strftime('%d-%m-%Y')

                            engagements = share_element_engagement.get("totalShareStatistics", {}).get("engagement", 0)
                            unique_impressions = share_element_engagement.get("totalShareStatistics", {}).get("uniqueImpressionsCount", 0)
                            share_count = share_element_engagement.get("totalShareStatistics", {}).get("shareCount", 0)
                            click_count = share_element_engagement.get("totalShareStatistics", {}).get("clickCount", 0)
                            like_count = share_element_engagement.get("totalShareStatistics", {}).get("likeCount", 0)
                            impression_count = share_element_engagement.get("totalShareStatistics", {}).get("impressionCount", 0)
                            comment_count = share_element_engagement.get("totalShareStatistics", {}).get("commentCount", 0)

                            engagements_in_range += engagements
                            unique_impressions_in_range += unique_impressions
                            share_count_in_range += share_count
                            click_count_in_range += click_count
                            like_count_in_range += like_count
                            impression_count_in_range += impression_count
                            comment_count_in_range += comment_count
                            days_count += 1
                            graph_data['engagement'].append({formatted_date: engagements})
                            graph_data['unique_impressions'].append({formatted_date: unique_impressions})
                            graph_data['share_count'].append({formatted_date: share_count})
                            graph_data['click_count'].append({formatted_date: click_count})
                            graph_data['like_count'].append({formatted_date: like_count})
                            graph_data['impression_count'].append({formatted_date: impression_count})
                            graph_data['comment_count'].append({formatted_date: comment_count})

                    graph_data['total_engagement'] = engagements_in_range
                    graph_data['total_unique_impressions'] = unique_impressions_in_range
                    graph_data['total_share_count'] = share_count_in_range
                    graph_data['total_click_count'] = click_count_in_range
                    graph_data['total_like_count'] = like_count_in_range
                    graph_data['total_impression_count'] = impression_count_in_range
                    graph_data['total_comment_count'] = comment_count_in_range
                    graph_data['cards'] = {
                        'daily_likes': like_count_in_range / days_count,
                        'likes_per_post': like_count_in_range / posts_in_range,
                        'daily_comments': comment_count_in_range / days_count,
                        'comments_per_post': comment_count_in_range / posts_in_range,
                        'daily_clicks': click_count_in_range / days_count,
                        'clicks_per_post': click_count_in_range / posts_in_range,
                    }

                    final_delivery_data = {
                        "graph_data": graph_data
                    }
                    return Response({'status': True, 'message': 'Data Found Successfully', 'data': final_delivery_data}, status=status.HTTP_200_OK)
                else:
                    return Response({'status': False, 'message': 'There was an issue fetching data'}, status=status.HTTP_400_BAD_REQUEST)

            else:
                return Response({'status': False, 'message': 'Please connect Linkedin'}, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


        
class InstagramAnalyticsDataView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def post(self,request):
        try:
            brand_id = request.headers.get("brand")
            try:
                auth_token = request.headers.get('Authorization')
                user_id = decode_token(auth_token)
                subscription_id = request.headers.get('subscription')

                local_user_id = request.headers.get('user')
                role_status, role_permission_result = check_multiple_roles_permissions(local_user_id, brand_id)
                if role_status == False:
                    return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
                if 3 not in role_permission_result :
                    return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
                
                check_sub = check_user_plan(user_id, int(subscription_id))
                print("check_sub", check_sub)

                instagram_access = check_sub['social_platforms'].get('instagram', False)

                if not instagram_access:
                    return Response({
                        'status': False,
                        'is_subscription': False,
                        'message': 'Your subscription does not include access to any social platforms'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                third_party = ThirdPartyAuth.objects.get(brand_id=brand_id)
                start_date = request.data.get('start_date')
                end_date = request.data.get('end_date')
                d_start_date = datetime.strptime(start_date, "%Y-%m-%d")
                d_end_date = datetime.strptime(end_date, "%Y-%m-%d")
                selected_days = [(d_start_date + timedelta(days=i)).strftime("%Y-%m-%d") 
                     for i in range((d_end_date - d_start_date).days + 1)]
                current_user_id = ''
                instagram_access_token = ''
                final_delivery_data = {} 

                if third_party.instagram_check:
                    current_user_id = third_party.insta_user_id
                    instagram_access_token = third_party.insta_auth_token
                    instagram_data_status, instagram_data = get_instagram_data(current_user_id,instagram_access_token,datetime.strptime(start_date,"%Y-%m-%d"),datetime.strptime(end_date,"%Y-%m-%d"))
                    if instagram_data_status:
                        final_delivery_data['graph_data'] = instagram_data

                        final_delivery_data['graph_data']['graph_1_metric'] = {
                            "followers_in_range": instagram_data.get('total_followers_graph', 0),
                            "daily_followers": instagram_data.get('total_followers_graph', 0) / len(selected_days) if len(selected_days) else 0,
                            "followers_per_post": instagram_data.get('total_followers_graph', 0) / instagram_data.get('total_posts_in_date_range', 0) if instagram_data.get('total_posts_in_date_range', 0) else 0,
                            "daily_posts": instagram_data.get('total_posts_in_date_range', 0) / len(selected_days) if len(selected_days) else 0,
                            "posts_per_week": instagram_data.get('total_posts_in_date_range', 0) / (len(selected_days) * 7) if len(selected_days) else 0,
                        }

                        final_delivery_data['graph_data']['graph_3_metric'] = {
                            "daily_likes": instagram_data.get('total_likes_in_date_range', 0) / len(selected_days) if len(selected_days) else 0,
                            "likes_per_post": instagram_data.get('total_likes_in_date_range', 0) / instagram_data.get('total_posts_in_date_range', 0) if instagram_data.get('total_posts_in_date_range', 0) else 0,
                            "comments_per_post": instagram_data.get('total_comments_in_date_range', 0) / instagram_data.get('total_posts_in_date_range', 0) if instagram_data.get('total_posts_in_date_range', 0) else 0,
                            "daily_comments": instagram_data.get('total_comments_in_date_range', 0) / len(selected_days) if len(selected_days) else 0,
                        }

                        return Response({'status':True,'message':'Data Found Successfully','data':final_delivery_data},status=status.HTTP_200_OK)
                    else:
                        return Response({'status':False,'message':'There was an issue fetching data'},status=status.HTTP_400_BAD_REQUEST)
                else:
                    return Response({'status':False,'message':'Please connect to Instagram'},status=status.HTTP_400_BAD_REQUEST)
            except ThirdPartyAuth.DoesNotExist:
                    return Response({'status':False,'message':'Please connect to Instagram'},status=status.HTTP_400_BAD_REQUEST)
        except Exception as e :
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

        
class InstagramDemographicDataView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def post(self,request):
        try:
            brand_id = request.headers.get("brand")
            try:
                auth_token = request.headers.get('Authorization')
                user_id = decode_token(auth_token)
                subscription_id = request.headers.get('subscription')

                local_user_id = request.headers.get('user')
                role_status, role_permission_result = check_multiple_roles_permissions(local_user_id, brand_id)
                if role_status == False:
                    return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
                if 3 not in role_permission_result :
                    return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
                
                check_sub = check_user_plan(user_id, int(subscription_id))
                print("check_sub", check_sub)

                instagram_access = check_sub['social_platforms'].get('instagram', False)

                if not instagram_access:
                    return Response({
                        'status': False,
                        'is_subscription': False,
                        'message': 'Your subscription does not include access to any social platforms'
                    }, status=status.HTTP_400_BAD_REQUEST)
                

                third_party = ThirdPartyAuth.objects.get(brand_id=brand_id)
                start_date = request.data.get('start_date')
                end_date = request.data.get('end_date')
                current_user_id = ''
                instagram_access_token = ''
                final_delivery_data = {
                    "age":[],
                    "city":[],
                    "country":[],
                    "gender":[],
                } 
                all_demography_list = ['age','city','country','gender']
                if third_party.instagram_check:
                    current_user_id = third_party.insta_user_id
                    instagram_access_token = third_party.insta_auth_token
                    i = 0
                    for params in all_demography_list:
                        instagram_data_status, instagram_data = get_instagram_insights(current_user_id,instagram_access_token,params)
                        if instagram_data_status:
                            total_followers = 0
                            for data in instagram_data:
                                    total_followers += data['value']

                            for graph_data in instagram_data:
                                g_entity_name = graph_data['dimension_values']
                                current_followers = graph_data['value']
                                final_delivery_data[params].append({
                                    "name":g_entity_name[0],
                                    "persanatge":  current_followers / total_followers * 100 
                                })

                    return Response({'status':True,'message':'Data Found Successfully','data':final_delivery_data},status=status.HTTP_200_OK)
                else:
                    return Response({'status':False,'message':'Please connect to Instagram'},status=status.HTTP_400_BAD_REQUEST)
            except ThirdPartyAuth.DoesNotExist:
                    return Response({'status':False,'message':'Please connect to Instagram'},status=status.HTTP_400_BAD_REQUEST)
        except Exception as e :
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        


class FacebookAnalyticsGraphOne(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def post(self,request):
        try:
            brand_id = request.headers.get("brand")
            try:
                auth_token = request.headers.get('Authorization')
                user_id = decode_token(auth_token)
                subscription_id = request.headers.get('subscription')

                local_user_id = request.headers.get('user')
                role_status, role_permission_result = check_multiple_roles_permissions(local_user_id, brand_id)
                if role_status == False:
                    return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
                if 3 not in role_permission_result :
                    return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
                
                check_sub = check_user_plan(user_id, int(subscription_id))
                print("check_sub", check_sub)

                facebook_access = check_sub['social_platforms'].get('facebook', False)

                if not facebook_access:
                    return Response({
                        'status': False,
                        'is_subscription': False,
                        'message': 'Your subscription does not include access to any social platforms'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                

                third_party = ThirdPartyAuth.objects.get(brand_id=brand_id)
                start_date = request.data.get('start_date')
                end_date = request.data.get('end_date')
                d_start_date = datetime.strptime(start_date, "%Y-%m-%d")
                d_end_date = datetime.strptime(end_date, "%Y-%m-%d")
                selected_days = [(d_start_date + timedelta(days=i)).strftime("%Y-%m-%d") 
                     for i in range((d_end_date - d_start_date).days + 1)]
                current_page_id = ''
                facebook_access_token = ''
                final_delivery_data = {} 
                total_posts = 0
                if third_party.facebook_check:
                    current_page_id = third_party.facebook_page_id
                    facebook_access_token = third_party.facebook_token

                    facebook_data_status , facebook_data = get_facebook_page_metrics(current_page_id,facebook_access_token,start_date,end_date)
                    facebook_post_status , facebook_post_data = get_post_count(current_page_id,facebook_access_token,start_date,end_date)
                    print(facebook_post_data)
                    if facebook_post_status: 
                        total_posts = facebook_post_data
                    if facebook_data_status:
                        final_delivery_data['metrics'] = {
                            "likes": facebook_data.get('total_fans', 0),
                            "daily_likes": facebook_data.get('total_fans', 0) / len(selected_days) if len(selected_days) else 0,
                            "likes_per_post": facebook_data.get('total_fans', 0) / total_posts if total_posts else 0,
                            "daily_page_view": facebook_data.get('total_views_total', 0) / len(selected_days) if len(selected_days) else 0,
                            "daily_posts": total_posts / len(selected_days) if len(selected_days) else 0,
                            "posts_per_week": total_posts / len(selected_days) / 7 if len(selected_days) else 0,
                        }
                        final_delivery_data['graph_data'] = facebook_data
                        return Response({'status':True,'message':'Data found successfully','data':final_delivery_data},status=status.HTTP_200_OK)
                    else:
                        return Response({'status':False,'message':'There was an issue finding the data','error':facebook_data},status=status.HTTP_400_BAD_REQUEST)
                else:
                    return Response({'status':False,'message':'Please connect to Facebook'},status=status.HTTP_400_BAD_REQUEST)

            except ThirdPartyAuth.DoesNotExist:
                return Response({'status':False,'message':'Please connect to Facebook'},status=status.HTTP_400_BAD_REQUEST)
        
        except Exception as e :
            return Response({'status': False, 'message': f'An error occurred: {str(e)}'}, status=status.HTTP_400_BAD_REQUEST) 
        

         
class FacebookAnalyticsGraphTwo(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def post(self,request):
        try:
            brand_id = request.headers.get("brand")
            try:
                auth_token = request.headers.get('Authorization')
                user_id = decode_token(auth_token)
                subscription_id = request.headers.get('subscription')

                local_user_id = request.headers.get('user')
                role_status, role_permission_result = check_multiple_roles_permissions(local_user_id, brand_id)
                if role_status == False:
                    return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
                if 3 not in role_permission_result :
                    return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
                
                check_sub = check_user_plan(user_id, int(subscription_id))
                print("check_sub", check_sub)

                facebook_access = check_sub['social_platforms'].get('facebook', False)

                if not facebook_access:
                    return Response({
                        'status': False,
                        'is_subscription': False,
                        'message': 'Your subscription does not include access to any social platforms'
                    }, status=status.HTTP_400_BAD_REQUEST)
                

                third_party = ThirdPartyAuth.objects.get(brand_id=brand_id)
                start_date = request.data.get('start_date')
                end_date = request.data.get('end_date')
                d_start_date = datetime.strptime(start_date, "%Y-%m-%d")
                d_end_date = datetime.strptime(end_date, "%Y-%m-%d")
                selected_days = [(d_start_date + timedelta(days=i)).strftime("%Y-%m-%d") 
                     for i in range((d_end_date - d_start_date).days + 1)]
                current_page_id = ''
                facebook_access_token = ''
                final_delivery_data = {} 
                total_posts = 0
                if third_party.facebook_check:
                    current_page_id = third_party.facebook_page_id
                    facebook_access_token = third_party.facebook_token

                    facebook_data_status , facebook_data = get_facebook_daily_insights(current_page_id,facebook_access_token,start_date,end_date)
                    if facebook_data_status:
                        final_delivery_data['metrics'] = {
                            "daily_reactions": facebook_data.get('total_impressions', 0) / len(selected_days) if len(selected_days) else 0,
                            "reaction_per_post": facebook_data.get('total_impressions', 0) / facebook_data.get('total_posts', 0) if facebook_data.get('total_posts', 0) else 0,
                            "daily_comments": facebook_data.get('total_comments', 0) / len(selected_days) if len(selected_days) else 0,
                            "comments_per_post": facebook_data.get('total_comments', 0) / facebook_data.get('total_posts', 0) if facebook_data.get('total_posts', 0) else 0,
                            "shares_per_day": facebook_data.get('total_shares', 0) / len(selected_days) if len(selected_days) else 0,
                            "shares_per_post": facebook_data.get('total_shares', 0) / facebook_data.get('total_posts', 0) if facebook_data.get('total_posts', 0) else 0,
                        }
                        final_delivery_data['graph_data'] = facebook_data
                        return Response({'status':True,'message':'Data found successfully','data':final_delivery_data},status=status.HTTP_200_OK)
                    else:
                        return Response({'status':False,'message':'There was an issue finding the data','error':facebook_data},status=status.HTTP_400_BAD_REQUEST)
                else:
                    return Response({'status':False,'message':'Please connect to Facebook'},status=status.HTTP_400_BAD_REQUEST)

            except ThirdPartyAuth.DoesNotExist:
                return Response({'status':False,'message':'Please connect to Facebook'},status=status.HTTP_400_BAD_REQUEST)
        
        except Exception as e :
            return Response({'status': False, 'message': f'An error occurred: {str(e)}'}, status=status.HTTP_400_BAD_REQUEST)  
        
        


class YouTubeAnalyticsView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def post(self, request):
        brand_id = request.headers.get("brand")
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            subscription_id = request.headers.get('subscription')

            local_user_id = request.headers.get('user')
            role_status, role_permission_result = check_multiple_roles_permissions(local_user_id, brand_id)
            if role_status == False:
                return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
            if 3 not in role_permission_result :
                return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
            
            check_sub = check_user_plan(user_id, int(subscription_id))
            print("check_sub", check_sub)

            youtube_access = check_sub['social_platforms'].get('youtube', False)

            if not youtube_access:
                return Response({
                    'status': False,
                    'is_subscription': False,
                    'message': 'Your subscription does not include access to any social platforms'
                }, status=status.HTTP_400_BAD_REQUEST)

            start_date = request.data.get("start_date")
            end_date = request.data.get("end_date")

            if not start_date or not end_date: 
                return Response({"status": False, "message": "Missing required parameters."}, status=status.HTTP_400_BAD_REQUEST)

            third_party = ThirdPartyAuth.objects.get(brand_id=brand_id)
            
            if not third_party.youtube_check or not third_party.youtube_refresh_token:
                return Response({"status": False, "message": "YouTube account is not connected."}, status=status.HTTP_400_BAD_REQUEST)
            
            success, data = get_youtube_analytics(f'youtube/{brand_id}-oauth2.json', start_date, end_date)
            subscriber_count = get_subscriber_count(f'youtube/{brand_id}-oauth2.json')
            if success:
                return Response({"status": True, "message": "Data found successfully", "data": data,"subscriber_count": int(subscriber_count) if subscriber_count is not None else 0}, status=status.HTTP_200_OK)
            else:
                return Response({"status": False, "message": "Failed to fetch data", "error": data}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e :
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)



class YouTubeVideoCountView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def post(self, request):
        brand_id = request.headers.get("brand")
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            subscription_id = request.headers.get('subscription')

            local_user_id = request.headers.get('user')
            role_status, role_permission_result = check_multiple_roles_permissions(local_user_id, brand_id)
            if role_status == False:
                return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
            if 3 not in role_permission_result :
                return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
            
            check_sub = check_user_plan(user_id, int(subscription_id))
            print("check_sub", check_sub)

            youtube_access = check_sub['social_platforms'].get('youtube', False)

            if not youtube_access:
                return Response({
                    'status': False,
                    'is_subscription': False,
                    'message': 'Your subscription does not include access to any social platforms'
                }, status=status.HTTP_400_BAD_REQUEST)
            

            start_date = request.data.get("start_date")
            end_date = request.data.get("end_date")

            third_party = ThirdPartyAuth.objects.get(brand_id=brand_id)
            
            if not third_party.youtube_check or not third_party.youtube_refresh_token:
                return Response({"status": False, "message": "YouTube account is not connected."}, status=status.HTTP_400_BAD_REQUEST)

            success, data = get_videos_count(f'youtube/{brand_id}-oauth2.json', start_date, end_date)
            if success:
                return Response({"status": True, "message": "Data found successfully", "data": data},status=status.HTTP_200_OK,)
            else:
                return Response({"status": False, "message": "Failed to fetch video count"},status=status.HTTP_400_BAD_REQUEST,)

        except Exception as e :
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)





class PinterestAnalyticsGraphOne(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def get(self, request):
        try:
            brand_id = request.headers.get("brand")
            if not brand_id:
                return Response({'status': False, 'message': 'Brand ID is required'}, status=status.HTTP_400_BAD_REQUEST)

            try:
                auth_token = request.headers.get('Authorization')
                user_id = decode_token(auth_token)
                subscription_id = request.headers.get('subscription')

                local_user_id = request.headers.get('user')
                role_status, role_permission_result = check_multiple_roles_permissions(local_user_id, brand_id)
                if role_status == False:
                    return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
                if 3 not in role_permission_result :
                    return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
                
                check_sub = check_user_plan(user_id, int(subscription_id))
                print("check_sub", check_sub)

                pinterest_access = check_sub['social_platforms'].get('pinterest', False)

                if not pinterest_access:
                    return Response({
                        'status': False,
                        'is_subscription': False,
                        'message': 'Your subscription does not include access to any social platforms'
                    }, status=status.HTTP_400_BAD_REQUEST)

                third_party = ThirdPartyAuth.objects.get(brand_id=brand_id)
                if not third_party.pinterest_check:
                    return Response({'status': False, 'message': 'Please connect to Pinterest'}, status=status.HTTP_400_BAD_REQUEST)



                pinterest_access_token = third_party.pinterest_creds
                profile_url = 'https://api.pinterest.com/v5/user_account'

                headers = {'Authorization': f'Bearer {pinterest_access_token}'}
                
                response = requests.get(profile_url, headers=headers)
                if response.status_code == 200:
                    profile_data = response.json()
                    print(profile_data)
                    filtered_data = {
                        'follower_count': profile_data.get('follower_count', 0),
                        'following_count': profile_data.get('following_count', 0),
                        'pin_count': profile_data.get('pin_count', 0),
                        'monthly_views': profile_data.get('monthly_views', 0),
                        'board_count': profile_data.get('board_count', 0),
                    }
                    return Response({'status': True, 'message': 'Data Found successfully', 'data': filtered_data}, status=status.HTTP_200_OK)
                else:
                    return Response({'status': False, 'message': 'There was an issue fetching data'}, status=status.HTTP_400_BAD_REQUEST)

            except ThirdPartyAuth.DoesNotExist:
                return Response({'status': False, 'message': 'Please connect to Pinterest'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class PinterestAnalyticsGraphTwo(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def post(self, request):
        try:
            brand_id = request.headers.get("brand")
            try:
                auth_token = request.headers.get('Authorization')
                user_id = decode_token(auth_token)
                subscription_id = request.headers.get('subscription')

                local_user_id = request.headers.get('user')
                role_status, role_permission_result = check_multiple_roles_permissions(local_user_id, brand_id)
                if role_status == False:
                    return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
                if 3 not in role_permission_result :
                    return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
                
                check_sub = check_user_plan(user_id, int(subscription_id))
                print("check_sub", check_sub)
                

                pinterest_access = check_sub['social_platforms'].get('pinterest', False)

                if not pinterest_access:
                    return Response({
                        'status': False,
                        'is_subscription': False,
                        'message': 'Your subscription does not include access to any social platforms'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                
                start_date = request.data.get('start_date')
                end_date = request.data.get('end_date')
                third_party = ThirdPartyAuth.objects.get(brand_id=brand_id)

                if not third_party.pinterest_check:
                    return Response({'status': False, 'message': 'Pinterest is not connected.'}, status=status.HTTP_400_BAD_REQUEST)


                access_token = third_party.pinterest_creds
                success, data = get_analytics_data(start_date, end_date, access_token)

                if success:
                    daily_total_interactions = 0
                    for daily_entry in data.get('daily_data', []):
                        daily_entry['INTERACTION'] = (daily_entry.get('PIN_CLICK', 0) +
                                                      daily_entry.get('OUTBOUND_CLICK', 0) +
                                                      daily_entry.get('SAVE', 0) +
                                                      daily_entry.get('ENGAGEMENT', 0))
                        daily_total_interactions += daily_entry['INTERACTION']
                    
                    response_data = {
                        'status': True,
                        'IMPRESSION': data.get('IMPRESSION'),
                        'ENGAGEMENT': data.get('ENGAGEMENT'),
                        'PIN_CLICK': data.get('PIN_CLICK'),
                        'OUTBOUND_CLICK': data.get('OUTBOUND_CLICK'),
                        'SAVE': data.get('SAVE'),
                        'INTERACTION': (data.get('PIN_CLICK', 0) +
                                        data.get('OUTBOUND_CLICK', 0) +
                                        data.get('SAVE', 0) +
                                        data.get('ENGAGEMENT', 0)),
                        'daily_data': data.get('daily_data', []),
                        'TOTAL_INTERACTION': daily_total_interactions
                    }
                    return Response(response_data, status=status.HTTP_200_OK)
                else:
                    return Response({'status': False, 'message': 'There was an issue fetching data'}, status=status.HTTP_400_BAD_REQUEST)

            except ThirdPartyAuth.DoesNotExist:
                return Response({'status': False, 'message': 'Please connect to Pinterest'}, status=status.HTTP_400_BAD_REQUEST)
        
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)






class ThreadsAnalyticsOne(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def post(self, request):
        try:
            brand_id = request.headers.get("brand")
            
            try:
                auth_token = request.headers.get('Authorization')
                user_id = decode_token(auth_token)
                subscription_id = request.headers.get('subscription')

                local_user_id = request.headers.get('user')
                role_status, role_permission_result = check_multiple_roles_permissions(local_user_id, brand_id)
                if role_status == False:
                    return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
                if 3 not in role_permission_result :
                    return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
                
                check_sub = check_user_plan(user_id, int(subscription_id))

                threads_access = check_sub['social_platforms'].get('threads', False)

                if not threads_access:
                    return Response({
                        'status': False,
                        'is_subscription': False,
                        'message': 'Your subscription does not include access to any social platforms'
                    }, status=status.HTTP_400_BAD_REQUEST)

                third_party = ThirdPartyAuth.objects.get(brand_id=brand_id)
                if not third_party.thread_check:
                    return Response({'status': False, 'message': 'Threads is not connected.'}, status=status.HTTP_400_BAD_REQUEST)
                
                access_token = third_party.thread_auth_token
                threads_id = third_party.thread_user_id
            

                insights = get_threads_analytics_one(access_token, threads_id)
                
                return Response({"status": True, "data": insights}, status=status.HTTP_200_OK)
            except ThirdPartyAuth.DoesNotExist:
                return Response({'status': False, 'message': 'Please connect to Threads'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)



class ThreadsAnalyticsTwo(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def post(self, request):
        try:
            brand_id = request.headers.get("brand")
            
            try:
                auth_token = request.headers.get('Authorization')
                user_id = decode_token(auth_token)
                subscription_id = request.headers.get('subscription')

                local_user_id = request.headers.get('user')
                role_status, role_permission_result = check_multiple_roles_permissions(local_user_id, brand_id)
                if role_status == False:
                    return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
                if 3 not in role_permission_result :
                    return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
                
                check_sub = check_user_plan(user_id, int(subscription_id))

                threads_access = check_sub['social_platforms'].get('threads', False)

                if not threads_access:
                    return Response({
                        'status': False,
                        'is_subscription': False,
                        'message': 'Your subscription does not include access to any social platforms'
                    }, status=status.HTTP_400_BAD_REQUEST)

                third_party = ThirdPartyAuth.objects.get(brand_id=brand_id)
                if not third_party.thread_check:
                    return Response({'status': False, 'message': 'Threads is not connected.'}, status=status.HTTP_400_BAD_REQUEST)
                
                access_token = third_party.thread_auth_token
                threads_id = third_party.thread_user_id
            

                insights = get_threads_analytics_two(access_token, threads_id)
                
                return Response({"status": True, "data": insights}, status=status.HTTP_200_OK)
            except ThirdPartyAuth.DoesNotExist:
                return Response({'status': False, 'message': 'Please connect to Threads'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        




class ThreadsAnalyticsThree(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def post(self, request):
        try:
            brand_id = request.headers.get("brand")
            
            try:
                auth_token = request.headers.get('Authorization')
                user_id = decode_token(auth_token)
                subscription_id = request.headers.get('subscription')

                local_user_id = request.headers.get('user')
                role_status, role_permission_result = check_multiple_roles_permissions(local_user_id, brand_id)
                if role_status == False:
                    return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
                if 3 not in role_permission_result :
                    return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
                
                
                check_sub = check_user_plan(user_id, int(subscription_id))
                
                threads_access = check_sub['social_platforms'].get('threads', False)

                if not threads_access:
                    return Response({
                        'status': False,
                        'is_subscription': False,
                        'message': 'Your subscription does not include access to any social platforms'
                    }, status=status.HTTP_400_BAD_REQUEST)

                third_party = ThirdPartyAuth.objects.get(brand_id=brand_id)
                if not third_party.thread_check:
                    return Response({'status': False, 'message': 'Threads is not connected.'}, status=status.HTTP_400_BAD_REQUEST)
                
                access_token = third_party.thread_auth_token
                threads_id = third_party.thread_user_id
                

                insights = get_threads_analytics_three(access_token, threads_id)
                
                return Response({"status": True, "data": insights}, status=status.HTTP_200_OK)
            except ThirdPartyAuth.DoesNotExist:
                return Response({'status': False, 'message': 'Please connect to Threads'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        


class ThreadsAnalyticsFour(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def post(self, request):
        try:
            brand_id = request.headers.get("brand")
            
            try:
                auth_token = request.headers.get('Authorization')
                user_id = decode_token(auth_token)
                subscription_id = request.headers.get('subscription')

                local_user_id = request.headers.get('user')
                role_status, role_permission_result = check_multiple_roles_permissions(local_user_id, brand_id)
                if role_status == False:
                    return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
                if 3 not in role_permission_result :
                    return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
                
                check_sub = check_user_plan(user_id, int(subscription_id))
                threads_access = check_sub['social_platforms'].get('threads', False)

                if not threads_access:
                    return Response({
                        'status': False,
                        'is_subscription': False,
                        'message': 'Your subscription does not include access to any social platforms'
                    }, status=status.HTTP_400_BAD_REQUEST)

                third_party = ThirdPartyAuth.objects.get(brand_id=brand_id)
                if not third_party.thread_check:
                    return Response({'status': False, 'message': 'Threads is not connected.'}, status=status.HTTP_400_BAD_REQUEST)
                
                access_token = third_party.thread_auth_token
                threads_id = third_party.thread_user_id
                

                insights = get_threads_analytics_four(access_token, threads_id)
                
                return Response({"status": True, "data": insights}, status=status.HTTP_200_OK)
            except ThirdPartyAuth.DoesNotExist:
                return Response({'status': False, 'message': 'Please connect to Threads'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        


class ThreadsAnalyticsFive(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def post(self, request):
        try:
            brand_id = request.headers.get("brand")
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            subscription_id = request.headers.get('subscription')

            start_date = request.data.get("start_date")
            end_date = request.data.get("end_date")

            local_user_id = request.headers.get('user')
            role_status, role_permission_result = check_multiple_roles_permissions(local_user_id, brand_id)
            if role_status == False:
                return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
            if 3 not in role_permission_result :
                return Response({'status':False,"message":'You Do Not Have Permission'},status=status.HTTP_400_BAD_REQUEST)
            
            check_sub = check_user_plan(user_id, int(subscription_id))
            threads_access = check_sub['social_platforms'].get('threads', False)

            if not threads_access:
                return Response({
                    'status': False,
                    'is_subscription': False,
                    'message': 'Your subscription does not include access to any social platforms'
                }, status=status.HTTP_400_BAD_REQUEST)

            
            if not start_date or not end_date:
                return Response({"status": False, "message": "start date and end date are required."}, status=status.HTTP_400_BAD_REQUEST)

            try:
                third_party = ThirdPartyAuth.objects.get(brand_id=brand_id)

                if not third_party.thread_check:
                    return Response({'status': False, 'message': 'Threads is not connected.'}, status=status.HTTP_400_BAD_REQUEST)

                access_token = third_party.thread_auth_token
                threads_id = third_party.thread_user_id

                success, analytics_data = threads_analytics_five(access_token, threads_id, start_date, end_date)

                if success:
                    return Response({"status": True, "data": analytics_data}, status=status.HTTP_200_OK)
                else:
                    return Response({"status": False, "message": "Failed to fetch analytics."}, status=status.HTTP_400_BAD_REQUEST)

            except ThirdPartyAuth.DoesNotExist:
                return Response({'status': False, 'message': 'Please connect to Threads'}, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)



class MastodonAnalyticsPostCountView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def post(self, request):
        try:
            brand_id = request.headers.get("brand")
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            subscription_id = request.headers.get('subscription')

            start_date = request.data.get("start_date")
            end_date = request.data.get("end_date")

            # check_sub = check_user_plan(user_id, int(subscription_id))
            # print(check_sub)
            # mastodon_access = check_sub['social_platforms'].get('threads', False)

            # if not mastodon_access:
            #     return Response({
            #         'status': False,
            #         'is_subscription': False,
            #         'message': 'Your subscription does not include access to any social platforms'
            #     }, status=status.HTTP_400_BAD_REQUEST)
            
            local_user_id = request.headers.get('user')
            role_status, role_permission_result = check_multiple_roles_permissions(local_user_id, brand_id)
            if role_status == False:
                return Response({'status': False, "message": 'You Do Not Have Permission'}, status=status.HTTP_400_BAD_REQUEST)
            if 3 not in role_permission_result:
                return Response({'status': False, "message": 'You Do Not Have Permission'}, status=status.HTTP_400_BAD_REQUEST)

            if not start_date or not end_date:
                return Response({"status": False, "message": "start date and end date are required."}, status=status.HTTP_400_BAD_REQUEST)

            try:
                third_party = ThirdPartyAuth.objects.get(brand_id=brand_id)

                if not third_party.mastodon_check:
                    return Response({'status': False, 'message': 'Mastodon is not connected.'}, status=status.HTTP_400_BAD_REQUEST)

                access_token = third_party.mastodon_token
                account_id = third_party.mastodon_user_id

                # Get all analytics
                post_success, post_counts = get_date_wise_post_counts(account_id, access_token)
                like_success, like_counts = get_date_wise_like_counts(account_id, access_token)
                reply_success, reply_counts = get_date_wise_reply_counts(account_id, access_token)
                reblog_success, reblog_counts = get_date_wise_reblog_counts(account_id, access_token)
                total_success, total_metrics = get_total_mastodon_metrics(account_id, access_token)

                if post_success or like_success or reply_success or reblog_success:
                    start = datetime.strptime(start_date, "%Y-%m-%d").date()
                    end = datetime.strptime(end_date, "%Y-%m-%d").date()
                    num_days = (end - start).days + 1
                    all_dates = [(start + timedelta(days=i)) for i in range(num_days)]
                    data = {}
                    for date in all_dates:
                        str_date = str(date)
                        data[str_date] = {
                            "posts": post_counts.get(date, 0) if post_success else 0,
                            "likes": like_counts.get(date, 0) if like_success else 0,
                            "replies": reply_counts.get(date, 0) if reply_success else 0,
                            "reblogs": reblog_counts.get(date, 0) if reblog_success else 0,
                        }
                    response = {"status": True, "data": data}
                    if total_success:
                        response["totals"] = total_metrics
                    return Response(response, status=status.HTTP_200_OK)
                else:
                    return Response({"status": False, "message": "Failed to fetch analytics."}, status=status.HTTP_400_BAD_REQUEST)

            except ThirdPartyAuth.DoesNotExist:
                return Response({'status': False, 'message': 'Please connect to Mastodon'}, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)



class SharePostWebLinkView(APIView):

    def post(self, request):
        try:
            post_id = request.data.get('post_id') # 'fis' for internal, 'fxs' for external
            if post_id :
                return Response({'status': False, 'message': 'Id Coverted','new_id':encrypt_data(str(post_id))}, status=status.HTTP_200_OK)
            else:
                return Response({'status': False, 'message': 'Id Not Coverted'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class SharePostView(APIView):

    def post(self, request):
        try:
            post_id = request.data.get('post_id')
            share_type = request.data.get('share_type')  # 'fis' for internal, 'fxs' for external
            if share_type not in ['fis', 'fxs']:
                return Response({'status': False, 'message': 'Invalid share type'}, status=status.HTTP_400_BAD_REQUEST)
            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''

            if not post_id or not share_type:
                return Response({'status': False, 'message': 'Post ID and share type are required'}, status=status.HTTP_400_BAD_REQUEST)

            post = Post.objects.get(id=post_id, is_deleted=False)
            if not post:
                return Response({'status': False, 'message': 'Post not found'}, status=status.HTTP_400_BAD_REQUEST)
            
            post_id = encrypt_data(post_id)
            share_url = f'{header}{domain}/api/post/share/{post_id}?share_type={share_type}'
            return Response({'status': True, 'message': 'Share link generated', 'share_url': share_url, 'share_type': share_type}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)



class SharedPostView(APIView):
    def get(self, request, id):
        try:
            if len(id) > 30:
                id = decrypt_data(id)
            else:
                id = id
            post = Post.objects.get(pk=id, is_deleted = False)
            if not post:
                return JsonResponse({'status': False, 'message': 'Post not found'}, status=status.HTTP_400_BAD_REQUEST)
            
            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''

            share_type = request.GET.get('share_type')
            post_data = {
                'id': post.pk,
                'title': post.title,
                'description': post.description,
                'location': post.location,
                'likes': LikePost.objects.filter(post_id=post.pk).count(),
                'dislikes': post.dislikes,
                'comments_count': post.comments_count,
                'tagged_in': post.tagged_in,
                'created_at': post.created_at,
                'files': [f'{header}{domain}{file.file.url}' for file in PostFiles.objects.filter(post_id=post.pk)],
                'latest_comment': '',
                'user': {
                    'user_id': post.user.pk,
                    'username': post.user.username,
                    'name': post.user.name,
                    'profile_image': f'{header}{domain}{post.user.profile_picture.url}' if post.user.profile_picture else ''
                },
                'is_liked': LikePost.objects.filter(post_id=post.pk, user_id=request.user.id).exists(),
                'is_saved': SavedPost.objects.filter(post_id=post.pk, user_id=request.user.id).exists(),
                'share_type': share_type,  
            }
            latest_comment = Comment.objects.filter(post=post).order_by('-pk').first()
            if latest_comment:
                post_data['latest_comment'] = f'{latest_comment.user.username} {latest_comment.comment_text}'
            return JsonResponse({'status': True, 'message': 'Post shared successfully', 'post_data': post_data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)



class ShareUserProfileView(APIView):
    def get(self, request):
        try:
            user_id = request.data.get('user_id')
            if not user_id:
                return JsonResponse({'status': False, 'message': 'user_id is required.'}, status=status.HTTP_400_BAD_REQUEST)

            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''

            try:
                user = UserRegistration.objects.get(pk=user_id)
            except UserRegistration.DoesNotExist:
                return JsonResponse({'status': False, 'message': 'User not found.'}, status=status.HTTP_400_BAD_REQUEST)

            share_url = f'{header}{domain}/api/chat-profile-view/{user_id}'

            return JsonResponse({'status': True, 'message': 'User share link generated.', 'share_url': share_url}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        

class SharedUserProfileView(APIView):
    def get(self, request, id):
        try:
            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''

            try:
                user = UserRegistration.objects.get(pk=id)
            except UserRegistration.DoesNotExist:
                return JsonResponse({'status': False, 'message': 'User not found.'}, status=status.HTTP_400_BAD_REQUEST)

            user_data = {
                'user_id': user.id,
                'username': user.username,
                'name': user.name,
                'profile_image': f'{header}{domain}{user.profile_picture.url}' if user.profile_picture else ''
            }

            return JsonResponse({'status': True, 'message': 'User data fetched successfully.', 'user_data': user_data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        

    
class SocialConnectUrlView(APIView):
    def get(self, request):
        try:
            brand_id = request.data.get('brand_id')  
            if not brand_id:
                return JsonResponse({'status': False, 'message': 'brand_id is required.'}, status=status.HTTP_400_BAD_REQUEST)

            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''

            if not CurrentBrand.objects.filter(brand__id=brand_id).exists():
                return JsonResponse({'status': False, 'message': 'No users found for this brand.'}, status=status.HTTP_404_NOT_FOUND)

            share_url = f'{header}{domain}/api/social-connect/{brand_id}'

            return JsonResponse({'status': True, 'message': 'User follow link generated.', 'share_url': share_url}, status=status.HTTP_200_OK)

        except Exception as e:
            return JsonResponse({'status': False, 'message': f'Error --> {str(e)}'}, status=status.HTTP_400_BAD_REQUEST)



class SocialConnectView(APIView):
    def get(self, request, id):
        try:
            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''

            try:
                brand = Brands.objects.get(pk=id)
                current_brand = CurrentBrand.objects.filter(brand=brand).first()
                if not current_brand:
                    return JsonResponse({'status': False, 'message': 'No user found for this brand.'}, status=status.HTTP_404_NOT_FOUND)
                user = current_brand.user
            except Brands.DoesNotExist:
                return JsonResponse({'status': False, 'message': 'Brand not found.'}, status=status.HTTP_404_NOT_FOUND)

            user_data = {
                'user_id': user.id,
                'username': user.username,
                'name': user.name,
                'profile_image': f'{header}{domain}{user.profile_picture.url}' if user.profile_picture else ''
            }

            return JsonResponse({'status': True, 'message': 'User data fetched successfully.', 'user_data': user_data}, status=status.HTTP_200_OK)
        except Exception as e:
            return JsonResponse({'status': False, 'message': f'Error --> {str(e)}'}, status=status.HTTP_400_BAD_REQUEST)

        


class FollowUserProfileUrlView(APIView):
    def get(self, request):
        try:
            user_id = request.data.get('user_id')
            if not user_id:
                return JsonResponse({'status': False, 'message': 'user_id is required.'}, status=status.HTTP_400_BAD_REQUEST)

            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''

            try:
                user = UserRegistration.objects.get(pk=user_id)
            except UserRegistration.DoesNotExist:
                return JsonResponse({'status': False, 'message': 'User not found.'}, status=status.HTTP_400_BAD_REQUEST)

            share_url = f'{header}{domain}/api/follow-profile-view/{user_id}'

            return JsonResponse({'status': True, 'message': 'User follow link generated.', 'share_url': share_url}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        


class FollowUserProfileView(APIView):
    def get(self, request, id):
        try:
            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''

            try:
                user = UserRegistration.objects.get(pk=id)
            except UserRegistration.DoesNotExist:
                return JsonResponse({'status': False, 'message': 'User not found.'}, status=status.HTTP_400_BAD_REQUEST)

            user_data = {
                'user_id': user.id,
                'username': user.username,
                'name': user.name,
                'profile_image': f'{header}{domain}{user.profile_picture.url}' if user.profile_picture else ''
            }

            return JsonResponse({'status': True, 'message': 'User data fetched successfully.', 'user_data': user_data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)



    
class CommentUserProfileUrlView(APIView):
    def get(self, request):
        try:
            post_id = request.data.get('post_id')
            if not post_id:
                return Response({'status': False, 'message': 'Post ID is required'}, status=status.HTTP_400_BAD_REQUEST)

            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''

            post = Post.objects.filter(id=post_id, is_deleted=False).first()
            if not post:
                return Response({'status': False, 'message': 'Post not found'}, status=status.HTTP_400_BAD_REQUEST)

            share_url = f'{header}{domain}/api/comment-profile-view/{post_id}'
            return Response({'status': True, 'message': 'Share link generated', 'share_url': share_url}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)



class CommentUserProfileView(APIView):
    def get(self, request, id):
        try:
            post = Post.objects.filter(pk=id, is_deleted=False).first()
            if not post:
                return JsonResponse({'status': False, 'message': 'Post not found'}, status=status.HTTP_400_BAD_REQUEST)

            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''

            post_data = {
                'id': post.pk,
                'title': post.title,
                'description': post.description,
                'location': post.location,
                'likes': LikePost.objects.filter(post_id=post.pk).count(),
                'dislikes': post.dislikes,
                'comments_count': post.comments_count,
                'tagged_in': post.tagged_in,
                'created_at': post.created_at,
                'files': [f'{header}{domain}{file.file.url}' for file in PostFiles.objects.filter(post_id=post.pk)],
                'latest_comment': '',
                'user': {
                    'user_id': post.user.pk,
                    'username': post.user.username,
                    'name': post.user.name,
                    'profile_image': f'{header}{domain}{post.user.profile_picture.url}' if post.user.profile_picture else ''
                },
                'is_liked': LikePost.objects.filter(post_id=post.pk, user_id=request.user.id).exists(),
                'is_saved': SavedPost.objects.filter(post_id=post.pk, user_id=request.user.id).exists(),
            }

            latest_comment = Comment.objects.filter(post=post).order_by('-pk').first()
            if latest_comment:
                post_data['latest_comment'] = f'{latest_comment.user.username} {latest_comment.comment_text}'

            return JsonResponse({'status': True, 'message': 'Post shared successfully', 'post_data': post_data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

class AppleFileView(APIView):
    def get(self,request):
        return JsonResponse({"applinks": {
        "apps": [],
        "details": [
        {
            "appID": "784953ZVC9.com.flowkar.app",
            "paths": ["*","/api/*"]
        }
        ]
    }})
    
class AndroifFileView(APIView):
    def get(self,request):
        return JsonResponse([{
  "relation": ["delegate_permission/common.handle_all_urls"],
  "target" : { "namespace": "android_app", "package_name": "com.app.flowkar",
               "sha256_cert_fingerprints": ["C2:17:65:2A:43:4C:84:6A:D8:1E:62:57:4B:C3:F5:AC:00:69:0F:1C:A9:BF:EB:AA:A6:CB:4F:71:B0:9E:14:84"] }
}],safe=False)
    


class SharePostMessageView(APIView):
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            if not auth_token:
                return Response({'status': False, 'message': 'JWT Token Required'}, status=status.HTTP_400_BAD_REQUEST)

            user_id = decode_token(auth_token)
            if not user_id:
                return Response({'status': False, 'message': 'Invalid JWT Token'}, status=status.HTTP_400_BAD_REQUEST)

            post_id = request.data.get('post_id')
            to_user_ids = request.data.get('to_user_id')
            type = request.data.get('type', 'share_post')
            reward_points = db_get_points('sharing_internal_post')


            if isinstance(to_user_ids, str):
                try:
                    to_user_ids = json.loads(to_user_ids)
                except json.JSONDecodeError:
                    if to_user_ids.isdigit():
                        to_user_ids = [int(to_user_ids)]
                    else:
                        return Response({'status': False, 'message': 'user id must be a list or valid JSON array'},status=status.HTTP_400_BAD_REQUEST)

            try:
                post = Post.objects.get(pk=post_id, is_deleted=False)
            except Post.DoesNotExist:
                return Response({'status': False, 'message': 'Post not found'}, status=status.HTTP_400_BAD_REQUEST)

            message = str(post_id)
            created_chats = []

            for to_user_id in to_user_ids:
                chat = ChatMessage.objects.create(
                    from_user_id=user_id,
                    to_user_id=to_user_id,
                    type=type,
                    messages=message
                )

                if post.user.user_type == "User":
                    if post.user.pk != user_id:
                        if post.shares <= 50:
                            db_update_points(post.user.pk,reward_points[1],"Post Share Reward Credited") #Post Owner
                    else:
                        pass
                else:   
                    if post.user.pk != user_id:
                        db_update_points(post.user.pk,reward_points[1],"Post Share Reward Credited")
                    else:
                        pass
                if post.user.pk != user_id:
                    db_update_points(user_id,reward_points[0],"Post Share Reward Credited") # The One Shairng
                    db_update_points(to_user_id,reward_points[0],"Post Share Reward Credited") # The One shared with
                else:
                    pass

                created_chats.append({
                    'id': chat.pk,
                    'message': post_id,
                    'type': 'share_post',
                    'post_id': post.pk,
                    'created_at': f'{timezone.localtime(chat.created_at).isoformat()}',
                    'sent_by': user_id,
                    'sent_to': to_user_id
                })
                post.shares += 1
                post.save()

            return Response({
                'status': True,
                'shared_with': created_chats
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)




#Deep linkning Views 


class SharePostViewDeeplink(View):
    def get(self, request, post_id):
        share_type = request.GET.get('share_type', '')

        encrypted_post_id = post_id
        post_id = decrypt_data(escape(post_id))
        enc_post_id = escape(post_id)
        share_type = escape(share_type)

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Open in Flowkar</title>
          <style>
            body {{ font-family: system-ui; text-align: center; padding: 40px 20px; }}
            h1 {{ margin-bottom: 30px; }}
            .btn {{ display: inline-block; background: #5856d6; color: white;
                    padding: 12px 24px; border-radius: 8px; text-decoration: none;
                    margin: 10px; font-weight: bold; }}
          </style>
        </head>
        <body>
          <h1>Opening Flowkar App...</h1>
          <p>If the app doesn't open automatically, click below:</p>
          <a href="flowkar://api.flowkar.com/api/post/share/{post_id}?share_type={share_type}" class="btn">Open in Flowkar</a>

          <script>
            var appUrl = "flowkar://api.flowkar.com/api/post/share/{post_id}?share_type={share_type}";
            var fallbackUrl = "https://flowkar.com/get-post/?post_id={encrypted_post_id}";
            window.location.href = appUrl;
            setTimeout(function() {{
              if (!document.hidden) {{
                window.location.href = fallbackUrl;
              }}
            }}, 2000);
          </script>
        </body>
        </html>
        """
        return HttpResponse(html_content, content_type='text/html')



class CommentUserProfileDeeplinkView(View):
    def get(self, request, post_id):
        post_id = escape(post_id)

        html_content = f""" 
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Open in Flowkar</title>
          <style>
            body {{ font-family: system-ui, sans-serif; text-align: center; padding: 40px 20px; }}
            h1 {{ margin-bottom: 30px; }}
            .btn {{
              display: inline-block;
              background: #5856d6;
              color: white;
              padding: 12px 24px;
              border-radius: 8px;
              text-decoration: none;
              margin: 10px;
              font-weight: bold;
            }}
            .store {{ background: #000; }}
          </style>
        </head>
        <body>
          <h1>Opening Flowkar App...</h1>
          <p>If the app doesn't open automatically, please click the button below:</p>
          <a href="flowkar://api.flowkar.com/api/comment-profile-view/{post_id}" class="btn">Open in Flowkar</a>
          <a href="https://apps.apple.com/app/flowkar/6740058663" class="btn store">Download App</a>

          <script>
            window.location.href = "flowkar://api.flowkar.com/api/comment-profile-view/{post_id}";
            setTimeout(function() {{
              if (document.visibilityState === "visible") {{
                if (/iPhone|iPad|iPod/.test(navigator.userAgent)) {{
                  window.location.href = "https://apps.apple.com/app/flowkar/6740058663";
                }} else {{
                  window.location.href = "https://play.google.com/store/apps/details?id=com.app.flowkar";
                }}
              }}
            }}, 2000);
          </script>
        </body>
        </html>
        """
        return HttpResponse(html_content, content_type='text/html')


class FollowUserProfileDeeplinkView(View):
    def get(self, request, user_id):
        user_id = escape(user_id)

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Open Profile in Flowkar</title>
          <style>
            body {{ font-family: system-ui, sans-serif; text-align: center; padding: 40px 20px; }}
            h1 {{ margin-bottom: 30px; }}
            .btn {{
              display: inline-block;
              background: #5856d6;
              color: white;
              padding: 12px 24px;
              border-radius: 8px;
              text-decoration: none;
              margin: 10px;
              font-weight: bold;
            }}
            .store {{ background: #000; }}
          </style>
        </head>
        <body>
          <h1>Opening Profile in Flowkar App...</h1>
          <p>If it doesn't open, click the button below:</p>
          <a href="flowkar://api.flowkar.com/api/follow-profile-view/{user_id}" class="btn">Open in App</a>
          <a href="https://apps.apple.com/app/flowkar/6740058663" class="btn store">Download App</a>

          <script>
            window.location.href = "flowkar://api.flowkar.com/api/follow-profile-view/{user_id}";
            setTimeout(function() {{
              if (document.visibilityState === "visible") {{
                if (/iPhone|iPad|iPod/.test(navigator.userAgent)) {{
                  window.location.href = "https://apps.apple.com/app/flowkar/6740058663";
                }} else {{
                  window.location.href = "https://play.google.com/store/apps/details?id=com.app.flowkar";
                }}
              }}
            }}, 2000);
          </script>
        </body>
        </html>
        """
        return HttpResponse(html_content, content_type='text/html')


class SocialConnectDeeplinkView(View):
    def get(self, request, brand_id):
        brand_id = escape(brand_id)

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Open Social Connect</title>
            <style>
                body {{
                    font-family: system-ui, sans-serif;
                    text-align: center;
                    padding: 40px 20px;
                }}
                h1 {{
                    margin-bottom: 30px;
                }}
                .btn {{
                    display: inline-block;
                    background: #5856d6;
                    color: white;
                    padding: 12px 24px;
                    border-radius: 8px;
                    text-decoration: none;
                    margin: 10px;
                    font-weight: bold;
                }}
                .store {{
                    background: #000;
                }}
            </style>
        </head>
        <body>
            <h1>Connecting to Brand Profile...</h1>
            <p>If it doesn't open, click the button below:</p>
            <a href="flowkar://api.flowkar.com/api/social-connect/{brand_id}" class="btn">Open in App</a>
            <a href="https://apps.apple.com/app/flowkar/6740058663" class="btn store">Download App</a>

            <script>
                window.location.href = "flowkar://api.flowkar.com/api/social-connect/{brand_id}";
                setTimeout(function() {{
                    if (document.visibilityState === "visible") {{  
                        if (/iPhone|iPad|iPod/.test(navigator.userAgent)) {{
                            window.location.href = "https://apps.apple.com/app/flowkar/6740058663";
                        }} 
                        else if (/Android/.test(navigator.userAgent)){{
                            window.location.href = "https://play.google.com/store/apps/details?id=com.app.flowkar";
                        }} else {{
                             window.location.href = "https://app.flowkar.com";
                        }}
                    }}
                }}, 2000);
            </script>
        </body>
        </html>
        """
        return HttpResponse(html_content, content_type='text/html')


class ChatProfileDeeplinkView(View):
    def get(self, request, user_id):
        user_id = escape(user_id)

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Open Chat Profile</title>
            <style>
                body {{
                    font-family: system-ui, sans-serif;
                    text-align: center;
                    padding: 40px 20px;
                }}
                h1 {{
                    margin-bottom: 30px;
                }}
                .btn {{
                    display: inline-block;
                    background: #5856d6;
                    color: white;
                    padding: 12px 24px;
                    border-radius: 8px;
                    text-decoration: none;
                    margin: 10px;
                    font-weight: bold;
                }}
                .store {{
                    background: #000;
                }}
            </style>
        </head>
        <body>
            <h1>Connecting to Chat Profile...</h1>
            <p>If it doesn't open, click the button below:</p>
            <a href="flowkar://api.flowkar.com/api/chat-profile-view/{user_id}" class="btn">Open in App</a>
            <a href="https://apps.apple.com/app/flowkar/6740058663" class="btn store">Download App</a>

            <script>
                window.location.href = "flowkar://api.flowkar.com/api/chat-profile-view/{user_id}";
                setTimeout(function() {{
                    if (document.visibilityState === "visible") {{
                        if (/iPhone|iPad|iPod/.test(navigator.userAgent)) {{
                            window.location.href = "https://apps.apple.com/app/flowkar/6740058663";
                        }} else {{
                            window.location.href = "https://play.google.com/store/apps/details?id=com.app.flowkar";
                        }}
                    }}
                }}, 2000);
            </script>
        </body>
        </html>
        """
        return HttpResponse(html_content, content_type='text/html')


class CurrentLocationView(APIView):
    def get(self, request):
        try:
            x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                client_ip = x_forwarded_for.split(',')[0]
            else:
                client_ip = request.META.get('REMOTE_ADDR')
            data = get_current_location(client_ip)

            if data:
                return Response({'status': True,'message': 'Location fetched successfully.','data': data}, status=status.HTTP_200_OK)
            else:
                return Response({'status': False,'message': 'Could not determine location.'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        

class CurrentAvailableAnalyticsPlatforms(APIView):
    def get(self, request):
        try:
            data = {
                'Facebook': True, 
                'Instagram': False,
                'Twitter': False,
                'YouTube': True, 
                'LinkedIn': True,
                'Pinterest': False, 
                'tiktok': True, 
                'threads': True, 
            }

            if data:
                return Response({'status': True,'message': 'Data fetched successfully.','data': data}, status=status.HTTP_200_OK)
            else:
                return Response({'status': False,'message': 'Could not determine location.'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        

def RazorpayCallbackView(request):
    webhook_body = request.body
    try:
            # Decode the bytes to string
        event_data = json.loads(webhook_body)
            
        return JsonResponse({
                'status': 'Webhook received',
                'event': event_data
            }, safe=False)
    except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': str(e),
                'web_body': f"{str(webhook_body)}"
            }, status=400)
    

class CreatePaymentLinkView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            user = UserRegistration.objects.get(id=user_id)
            amount = request.data.get('amount')
            payment_link = create_payment_link(user_id,int(amount), user.name,user.enc_email)
            return Response({'status': True,'message':'Payment link created successfully.','data':payment_link},status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

#Reward 

class RewardScreenView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            user = UserRegistration.objects.get(id=user_id)
            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''
            user_points = db_get_user_points(user_id)
            user_coin = db_get_user_coins(user_id)
            user_reward_transaction = db_get_user_reward_transaction(user_id)
            user_obj = {
                "id":user.pk,
                "name":user.name,
                "profile_picture":f"{header}{domain}{user.profile_picture.url}" if user.profile_picture else "",
                "points":user_points,
                "coins":user_coin,
                "reward_transaction":user_reward_transaction[::-1]
            }
            return Response({'status': True,'message':'Data found successfully.','data':user_obj},status=status.HTTP_200_OK)
        
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        
class LeaderBoardView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''
            leaderboard_data = db_get_leaderboard_data()
            leaderboard_data_list = []
            index = 1
            for data in leaderboard_data:
                print(data['_id'])
                try:
                    user_data = UserRegistration.objects.get(id=data['_id'])
                    leaderboard_data_list.append({
                        "rank":index,
                        "user_id":data['_id'],
                        "name":user_data.name,
                        "points":data['points'],
                        "profile_picture":f"{header}{domain}{user_data.profile_picture.url}" if user_data.profile_picture else ""
                    })
                    index += 1
                except UserRegistration.DoesNotExist:
                    pass
            return Response({'status': True,'message':'Data found successfully.','data':leaderboard_data_list},status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

class GetLiveUsersListView(APIView):
    def get(self, request):
        try:
            data = get_live_users()
            response_data = []
            for user in data:
                response_data.append({
                    "user_id":user['_id'],
                    "username":user['name'],
                    "userprofile":user['profile_picture'],
                    "is_live":True,
                    "stories":[]
                        
                })
            return Response({'status': True,'message':'Data found successfully.','data':response_data},status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class TodayScheduledPostsView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def get(self, request):
        auth_token = request.headers.get('Authorization')
        user_id = decode_token(auth_token)

        today = datetime.now().strftime('%Y-%m-%d')

        try:
            posts = Post.objects.filter(
                user_id=user_id,
                is_scheduled=True,
                scheduled_at__startswith=today
            ).order_by('scheduled_at') 

            platform_latest_posts = {}

            for post in posts:
                scheduled_time = post.scheduled_at
                if isinstance(scheduled_time, datetime):
                    uploadtime = scheduled_time.strftime('%Y-%m-%d %H:%M:%S.000')
                else:
                    uploadtime = scheduled_time

                if post.facebook and 'Facebook' not in platform_latest_posts:
                    platform_latest_posts['Facebook'] = {"Facebook": True, "uploadtime": uploadtime}
                if post.instagram and 'Instagram' not in platform_latest_posts:
                    platform_latest_posts['Instagram'] = {"Instagram": True, "uploadtime": uploadtime}
                if post.linkedin and 'LinkedIn' not in platform_latest_posts:
                    platform_latest_posts['LinkedIn'] = {"LinkedIn": True, "uploadtime": uploadtime}
                if post.pinterest and 'Pinterest' not in platform_latest_posts:
                    platform_latest_posts['Pinterest'] = {"Pinterest": True, "uploadtime": uploadtime}
                if post.youtube and 'YouTube' not in platform_latest_posts:
                    platform_latest_posts['YouTube'] = {"YouTube": True, "uploadtime": uploadtime}
                if post.vimeo and 'Vimeo' not in platform_latest_posts:
                    platform_latest_posts['Vimeo'] = {"Vimeo": True, "uploadtime": uploadtime}
                if post.twitter and 'threads' not in platform_latest_posts:
                    platform_latest_posts['threads'] = {"threads": True, "uploadtime": uploadtime}
                if post.x and 'x' not in platform_latest_posts:
                    platform_latest_posts['x'] = {"x": True, "uploadtime": uploadtime}
                if post.tumblr and 'tumblr' not in platform_latest_posts:
                    platform_latest_posts['tumblr'] = {"tumblr": True, "uploadtime": uploadtime}
                if post.reddit and 'reddit' not in platform_latest_posts:
                    platform_latest_posts['reddit'] = {"reddit": True, "uploadtime": uploadtime}
                if post.tiktok and 'tiktok' not in platform_latest_posts:
                    platform_latest_posts['tiktok'] = {"tiktok": True, "uploadtime": uploadtime}

            result = list(platform_latest_posts.values())
            
            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"status": False, "message": f"Error --> {str(e)}"}, status=status.HTTP_400_BAD_REQUEST)
        

class GetRewardDataView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            user_7_day_status = db_get_user_activity_status_7(user_id)
            recurrring_data = get_user_transaction_counts_flexible(user_id)
            profile_completion_status, persantage = get_profile_completion_status(user_id)
            data = {
                "user_7_day_status":user_7_day_status,
                "recurrring_data":recurrring_data,
                "profile_completion_status":profile_completion_status,
                "persantage":persantage
            }
            return Response({'status': True,'message':'Data found successfully.','data':data},status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)



class ScheduledPostWebApiView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def get(self, request):
        auth_token = request.headers.get('Authorization')
        token_user_id = decode_token(auth_token)
        unscheduled_post = request.query_params.get('unscheduled_post')
        platform = request.query_params.get('platform')  # New query parameter for platform
        date = datetime.now()
        tomorrow = datetime.now() + timedelta(days=1)
        formatted_date = date.strftime('%Y-%m-%d')
        month = date.strftime('%m')
        tomorrow_date = tomorrow.strftime('%Y-%m-%d')
        try:
            if unscheduled_post == '1':
                post_data = Post.objects.filter(Q(user_id=token_user_id), Q(is_scheduled=False), Q(
                    is_posted=False), Q(is_unscheduled=True))
            else:
                post_data = Post.objects.filter(
                    Q(user_id=token_user_id), Q(is_scheduled=True))
            
            # Filter by platform if specified
            if platform:
                platform = platform.lower()
                if platform == 'facebook':
                    post_data = post_data.filter(facebook=True)
                elif platform == 'instagram':
                    post_data = post_data.filter(instagram=True)
                elif platform == 'linkedin':
                    post_data = post_data.filter(linkedin=True)
                elif platform == 'pinterest':
                    post_data = post_data.filter(pinterest=True)
                elif platform == 'threads':
                    post_data = post_data.filter(twitter=True)
                elif platform == 'youtube':
                    post_data = post_data.filter(youtube=True)
                elif platform == 'tiktok':
                    post_data = post_data.filter(tiktok=True)
                elif platform == 'tumblr':
                    post_data = post_data.filter(tumblr=True)
                elif platform == 'reddit':
                    post_data = post_data.filter(reddit=True)
                elif platform == 'vimeo':
                    post_data = post_data.filter(vimeo=True)
                elif platform == 'x':
                    post_data = post_data.filter(x=True)

            today_posts = []
            tomorrow_posts = []
            this_month_posts = []
            for data in post_data:
                schedule_date = data.scheduled_at.split()[0]
                try:
                    which_month = schedule_date.split('-')[1]
                except IndexError:
                    which_month = ''
                if schedule_date == formatted_date:
                    today_posts.append(data)
                if schedule_date == tomorrow_date:
                    tomorrow_posts.append(data)
                if which_month == month:
                    this_month_posts.append(data)
            # Platform names mapping
            platform_names = {
                'facebook': 'Facebook',
                'instagram': 'Instagram',
                'linkedin': 'LinkedIn',
                'pinterest': 'Pinterest',
                'twitter': 'Threads',
                'youtube': 'YouTube',
                'tiktok': 'TikTok',
                'tumblr': 'Tumblr',
                'reddit': 'Reddit',
                'vimeo': 'Vimeo',
                'x': 'X'
            }

            def extract_platform_dict(post):
                platform_data = {}
                for field, name in platform_names.items():
                    if getattr(post, field, False):
                        platform_data[name] = True
                return platform_data

            # Serialize data and add platform information
            response_data = []
            for post in post_data:
                post_data_serialized = PostViewScheduleSerializer(post, context={'request': request}).data
                post_data_serialized['platform'] = extract_platform_dict(post)
                response_data.append(post_data_serialized)

            today = len(today_posts)
            tomorrow = len(tomorrow_posts)
            this_month = len(this_month_posts)
            return Response({'status': True, 'message': 'Data Found Successfully', 'today': today, 'tomorrow': tomorrow, 'this_month': this_month, 'data': response_data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)



class ScheduledAndPostedPostWebApiView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    pagination_class = CustomPagination

    @sentry_sdk.trace
    def get(self, request):
        auth_token = request.headers.get('Authorization')
        user_id = decode_token(auth_token)

        try:
            search_query = request.query_params.get('search')
            platform_filter = request.query_params.get('platform')
            
            posts = Post.objects.filter(user_id=user_id, is_deleted=False).order_by('-created_at')
            
            if search_query:
                posts = posts.filter(Q(title__icontains=search_query) | Q(description__icontains=search_query))

            if platform_filter:
                platform_field_map = {
                    'facebook': 'facebook',
                    'instagram': 'instagram',
                    'linkedin': 'linkedin',
                    'pinterest': 'pinterest',
                    'threads': 'twitter',  
                    'youtube': 'youtube',
                    'tiktok': 'tiktok',
                    'tumblr': 'tumblr',
                    'reddit': 'reddit',
                    'vimeo': 'vimeo',
                    'x': 'x',
                    'mastodon': 'mastadon'
                }
                
                if platform_filter in platform_field_map:
                    field_name = platform_field_map[platform_filter]
                    filter_kwargs = {field_name: True}
                    posts = posts.filter(**filter_kwargs)

            scheduled_posts = posts.filter(is_scheduled=True, is_deleted=False)
            posted_posts = posts.filter(is_posted=True, is_deleted=False)
            
            combined_posts = list(scheduled_posts) + list(posted_posts)
            
            platform_names = {
                'facebook': 'Facebook',
                'instagram': 'Instagram',
                'linkedin': 'LinkedIn',
                'pinterest': 'Pinterest',
                'twitter': 'Threads',
                'youtube': 'YouTube',
                'tiktok': 'TikTok',
                'tumblr': 'Tumblr',
                'reddit': 'Reddit',
                'vimeo': 'Vimeo',
                'x': 'X',
                'mastadon': 'Mastodon'
            }

            def extract_platform_dict(post):
                platform_data = {}
                for field, name in platform_names.items():
                    if getattr(post, field, False):
                        platform_data[name] = True 
                return platform_data

            response_data = []
            for post in combined_posts:
                post_data = PostViewScheduleSerializer(post, context={'request': request}).data
                post_data['status'] = 'scheduled' if post.is_scheduled else 'posted'
                post_data['platform'] = extract_platform_dict(post)
                
                # Add width and height like GetPostView
                files = post_data.get('files', [])
                width, height = 0, 0
                if files:
                    try:
                        width, height = get_image_dimensions(files[0])
                    except Exception as e:
                        width, height = 0, 0
                post_data['width'] = width
                post_data['height'] = height

                response_data.append(post_data)

            paginator = self.pagination_class()
            paginated_data = paginator.paginate_queryset(response_data, request)
            
            return paginator.get_paginated_response({'status': True,'message': 'Data found Successfully','data': paginated_data})

        except Exception as e:
            return Response({'status': False,'message': f'Error --> {str(e)}'}, status=status.HTTP_400_BAD_REQUEST)

            


class PostDeleteOrUnscheduledView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)

            post_id = request.query_params.get('post_id')
            if not post_id:
                return Response({'status': False, 'message': 'post_id is required'}, status=status.HTTP_400_BAD_REQUEST)

            try:
                post = Post.objects.get(pk=post_id, user_id=user_id)
                post.is_deleted = True

                if post.is_scheduled:
                    post.is_scheduled = False
                    post.is_unscheduled = True
                    message = 'Scheduled Post Deleted Successfully'
                else:
                    message = 'Post Deleted Successfully'

                post.save()

                return Response({'status': True, 'message': message}, status=status.HTTP_200_OK)

            except Post.DoesNotExist:
                return Response({'status': False, 'message': 'Post Not Found'}, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class AutoAssignCurrentBrandView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def get(self, request):
        try:
            # Get all users
            all_users = UserRegistration.objects.all()
            assigned_count = 0
            skipped_count = 0
            
            for user in all_users:
                try:
                    # Find brand where user is owner and name matches user's name
                    brand = Brands.objects.get(user_id=user.id, name=user.name)
                    
                    # Update or create current brand
                    current_brand, created = CurrentBrand.objects.update_or_create(
                        user_id=user.id,
                        defaults={'brand': brand}
                    )
                    assigned_count += 1
                    
                except Brands.DoesNotExist:
                    # If no matching brand found, skip this user
                    skipped_count += 1
                    continue
            
            return Response({
                'status': True,
                'message': f'Brand assignment completed. Assigned: {assigned_count}, Skipped: {skipped_count}'
            }, status=status.HTTP_200_OK)
                
        except Exception as e:
            return Response({
                'status': False,
                'message': f'Error --> {e}'
            }, status=status.HTTP_400_BAD_REQUEST)


class TelegramDialogsView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            brand_id = request.headers.get('brand')

            # limit = int(request.query_params.get('limit', 100))

            session_name = f'telegram_session_{user_id}_{brand_id}'

            result = get_telegram_dialogs(limit=None, session_name=session_name)

            if result['success']:

                return Response({
                    'status': True,
                    'message': 'Dialogs retrieved successfully',
                    'data': result['dialogs'],
                    'count': result['count']
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'status': False,
                    'message': result['message'],
                    'error': result.get('error')
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                'status': False,
                'message': f'Error --> {e}'
            }, status=status.HTTP_400_BAD_REQUEST)


class TelegramSendMessageView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            brand_id = request.headers.get('brand')

            chat_id = request.data.get('chat_id')
            message_text = request.data.get('message')
            file = request.FILES.get('file')

            if not chat_id:
                return Response({'status': False,'message': 'chat_id is required'}, status=status.HTTP_400_BAD_REQUEST)

            if not message_text and not file:
                return Response({
                    'status': False,
                    'message': 'Either message text or file is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            session_name = f'telegram_session_{user_id}_{brand_id}'

            file_path = None
            if file:

                temp_dir = tempfile.gettempdir()
                file_extension = os.path.splitext(file.name)[1] if file.name else ''
                temp_file = tempfile.NamedTemporaryFile(
                    delete=False,
                    suffix=file_extension,
                    dir=temp_dir
                )

                # Write file content
                for chunk in file.chunks():
                    temp_file.write(chunk)
                temp_file.close()

                file_path = temp_file.name

            try:
                # Send message
                result = send_telegram_message(chat_id, message_text, file_path, session_name)

                if result['success']:
                    print(f"Message sent to chat {chat_id}")

                    return Response({
                        'status': True,
                        'message': result['message'],
                        'data': result['sent_message']
                    }, status=status.HTTP_200_OK)
                else:
                    return Response({
                        'status': False,
                        'message': result['message'],
                        'error': result.get('error')
                    }, status=status.HTTP_400_BAD_REQUEST)

            finally:
                if file_path and os.path.exists(file_path):
                    try:
                        os.unlink(file_path)
                    except Exception:
                        pass 

        except Exception as e:
            return Response({
                'status': False,
                'message': f'Error --> {e}'
            }, status=status.HTTP_400_BAD_REQUEST)


class TelegramUserChatsView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            brand_id = request.headers.get('brand')

            target_user_id = request.query_params.get('target_user_id')
            if not target_user_id:
                return Response({
                    'status': False,
                    'message': 'target_user_id is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            session_name = f'telegram_session_{user_id}_{brand_id}'

            result = get_telegram_user_chats(target_user_id, limit=None, offset_id=0, session_name=session_name)

            if result['success']:
                print(f"Retrieved ALL {result['count']} messages for user {target_user_id}")

                return Response({
                    'status': True,
                    'message': f'Complete chat history retrieved successfully for user {target_user_id}',
                    'target_user_id': target_user_id,
                    'data': result['messages'],
                    'total_count': result['count'],
                    'note': 'All messages from this conversation are included'
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'status': False,
                    'message': result['message'],
                    'error': result.get('error')
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                'status': False,
                'message': f'Error --> {e}'
            }, status=status.HTTP_400_BAD_REQUEST)


class TelegramLogoutView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            brand_id = request.headers.get('brand')

            session_name = f'telegram_session_{user_id}_{brand_id}'

            success, result = telegram_logout(session_name)

            if success:
                third_party_auth = ThirdPartyAuth.objects.get(brand_id=brand_id)
                third_party_auth.telegram_check = False
                third_party_auth.save()
                return Response({'status': True,'message': 'Logged out successfully','Connect':False}, status=status.HTTP_200_OK)
            else:
                return Response({'status': False,'message': result['message'],}, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                'status': False,
                'message': f'Error --> {e}'
            }, status=status.HTTP_400_BAD_REQUEST)



class AiGenerateResponseView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            image_url = request.FILES.get('media')
            prompt = request.data.get('prompt')
            is_seeking_generation = request.data.get('is_seeking_generation')

            
            if image_url:
            # Create media/ai directory if it doesn't exist
                media_ai_dir = os.path.join(settings.MEDIA_ROOT, 'ai')
                os.makedirs(media_ai_dir, exist_ok=True)

                # Generate unique filename
                timestamp = int(time.time())
                file_extension = os.path.splitext(image_url.name)[
                    1] if image_url.name else '.jpg'
                filename = f"ai_{user_id}_{timestamp}{file_extension}"
                file_path = os.path.join(media_ai_dir, filename)

                # Save the uploaded file
                with open(file_path, 'wb+') as destination:
                    for chunk in image_url.chunks():
                        destination.write(chunk)

                try:
                    if is_seeking_generation == '1':
                        ai_response = generate_media_content(file_path,'auto',prompt)
                    else:
                        ai_response = generate_media_content(file_path)
                    
                    return Response({
                            'status': True,
                            'message': 'AI response generated successfully',
                            'result': ai_response['content'],
                            'title': ai_response['title']
                        }, status=status.HTTP_200_OK)

                finally:
                    # Clean up: Delete the file after processing
                    if os.path.exists(file_path):
                        os.remove(file_path)
            return Response({'status': True, 'message': 'AI response generated successfully', 'result': ai_response['content'], 'title': ai_response['title']}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {str(e)}'}, status=status.HTTP_400_BAD_REQUEST)


class AiGenerateDuplicatePostView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            image_url = request.FILES.get('media')
            prompt = request.data.get('prompt')

            if image_url:
                media_ai_dir = os.path.join(settings.MEDIA_ROOT, 'ai')
                os.makedirs(media_ai_dir, exist_ok=True)

                # Generate unique filename
                timestamp = int(time.time())
                file_extension = os.path.splitext(image_url.name)[
                    1] if image_url.name else '.jpg'
                filename = f"ai_{user_id}_{timestamp}{file_extension}"
                file_path = os.path.join(media_ai_dir, filename)

                # Save the uploaded file
                with open(file_path, 'wb+') as destination:
                    for chunk in image_url.chunks():
                        destination.write(chunk)

                try:
                    ai_response = create_complete_duplicate(file_path,prompt)
                    # Extract only the relative path from the full path
                    full_path = ai_response['generated_image_path']
                    if full_path and '/media/' in full_path:
                        relative_path = '/media/' + full_path.split('/media/')[-1]
                    else:
                        relative_path = full_path
                    
                    return Response({
                            'status': True,
                            'message': 'AI response generated successfully',
                            'result': ai_response['text_content'],
                            'generated_image_path': relative_path
                        }, status=status.HTTP_200_OK)
                finally:
                    # Clean up: Delete the file after processing
                    if os.path.exists(file_path):
                        os.remove(file_path)

            else:
                return Response({'status': False, 'message': 'Image is required'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {str(e)}'}, status=status.HTTP_400_BAD_REQUEST)

class AiGenerateCommentView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            comment_text = request.data.get('comment_text')
            comment = generate_social_comment(comment_text)
            return Response({'status': True, 'message': 'AI comment generated successfully', 'result': comment}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {str(e)}'}, status=status.HTTP_400_BAD_REQUEST)
        

class AiGenerateMessageReplyView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            user_id = decode_token(auth_token)
            message_text = request.data.get('message_text')
            reply = generate_quick_reply(message_text)
            return Response({'status': True, 'message': 'AI message reply generated successfully', 'result': reply}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {str(e)}'}, status=status.HTTP_400_BAD_REQUEST)


#Post Approval

class PendingPostApprovalView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    @sentry_sdk.trace
    def get(self, request):
        auth_token = request.headers.get('Authorization')
        token_user_id = decode_token(auth_token)
        try:
            post_data = Post.objects.filter(Q(user_id=token_user_id), Q(is_approved=False))
            serializer = PostViewScheduleSerializer(
                post_data, many=True, context={'request': request})
            return Response({'status': True, 'message': 'Data Found Successfully', 'data': serializer.data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

class ApprovePostUpload(APIView):
    def post(self, request):
        auth_token = request.headers.get('Authorization')
        token_user_id = decode_token(auth_token)
        post_id = request.data.get('post_id')
        
        try:
            domain = request.get_host()
            if '127.0.0.1' in domain:
                header = 'https://'
            else:
                header = 'https://'
        except ValueError:
            header = ''
            domain = ''
        image_formats = [
            'jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'tif',
            'webp', 'svg', 'ico', 'heif', 'heic', 'raw', 'nef',
                    'cr2', 'orf', 'arw', 'dng'
        ]
        video_formats = [
            'mp4', 'mkv', 'mov', 'avi', 'flv', 'wmv', 'webm',
            'm4v', 'mpg', 'mpeg', '3gp', '3g2', 'mts', 'm2ts',
            'ts', 'ogv', 'rm', 'rmvb'
        ]
        statuses = {}
        
        try:
                    post = Post.objects.get(pk=post_id)
                    # Update post status
                    post.is_posted = True
                    post.is_approved = True
                    post.save()

                    # Get third party auth data
                    third_party_data = ThirdPartyAuth.objects.get(brand_id=post.brand_id)
                    
                    # Get all files for the post
                    post_files = PostFiles.objects.filter(post=post)
                    all_files = [file.file.url for file in post_files]
                    original_files = ['media/' + file.split('/media/')[-1] for file in all_files]

                    # Handle Instagram upload
                    if post.instagram and third_party_data.instagram_check:
                        try:
                            all_images = []
                            all_video = []
                            upload = False
                            for insta_files in all_files:
                                file_format = insta_files.split(".")[-1].split('.')[-1]
                                if file_format.lower() in image_formats:
                                    all_images.append(insta_files)
                                elif file_format.lower() in video_formats:
                                    all_video.append(insta_files)

                            if len(all_video) > 0:
                                upload, instagram_upload_id_video = upload_and_publish_instagram_video(
                                    third_party_data.insta_user_id, third_party_data.insta_auth_token, all_video[0], post.description)
                                if upload:
                                    post.instagram_id = instagram_upload_id_video

                            if len(all_images) > 0:
                                upload, instagram_upload_id = upload_images_to_instagram(
                                    third_party_data.insta_user_id, third_party_data.insta_auth_token, all_images, post.description)
                                if upload:
                                    post.instagram_id = instagram_upload_id
                            
                            statuses['Instagram'] = upload
                            post.save()
                        except Exception as e:
                            statuses['Instagram'] = False
                            print(f"Instagram upload error: {str(e)}")

                    # Handle Facebook upload
                    if post.facebook and third_party_data.facebook_check:
                        try:
                            all_images = []
                            all_video = []
                            upload = False
                            for facebook_files in all_files:
                                file_format = facebook_files.split(".")[-1].split('.')[-1]
                                if file_format.lower() in image_formats:
                                    all_images.append(facebook_files)
                                else:
                                    all_video.append(facebook_files)

                            if len(all_video) > 0:
                                upload, facebook_upload_id_video = upload_facebook_videos(
                                    third_party_data.facebook_page_id, third_party_data.facebook_token, all_video[0], post.description)
                                if upload:
                                    post.facebook_id = facebook_upload_id_video

                            if len(all_images) > 0:
                                upload, facebook_upload_id = upload_facebook_images(
                                    third_party_data.facebook_page_id, third_party_data.facebook_token, all_images, post.description)
                                if upload:
                                    post.facebook_id = facebook_upload_id

                            statuses['Facebook'] = upload
                            post.save()
                        except Exception as e:
                            statuses['Facebook'] = False
                            print(f"Facebook upload error: {str(e)}")

                    # Handle LinkedIn upload
                    if post.linkedin and third_party_data.linkedin_check:
                        try:
                            video_array = []
                            image_array = []
                            for data in original_files:
                                file_format = data.split(".")[-1].split('.')[-1]
                                if file_format.lower() in video_formats:
                                    video_array.append(data)
                                elif file_format.lower() in image_formats:
                                    image_array.append(data)

                            if len(video_array) > 0:
                                for upload_linkedin in video_array:
                                    media_types = [f'video/{upload_linkedin.split(".")[-1]}']
                                    upload_linkedin_video, uploaded_linkedin_id = create_linkedin_post_with_video(
                                        third_party_data.linked_in_token, third_party_data.linkedin_creds, post.description, [upload_linkedin], media_types)
                                    if upload_linkedin_video:
                                        post.linkedin_id = uploaded_linkedin_id
                                        statuses['LinkedIn'] = True

                            if len(image_array) > 0:
                                media_types = [f'image/{file.split(".")[-1]}' for file in image_array]
                                upload_linkedin_photo, uploaded_linkedin_id = create_linkedin_post_with_multiple_media(
                                    third_party_data.linked_in_token, third_party_data.linkedin_creds, post.description, original_files, media_types)
                                if upload_linkedin_photo:
                                    post.linkedin_id = uploaded_linkedin_id
                                    statuses['LinkedIn'] = True

                            post.save()
                        except Exception as e:
                            statuses['LinkedIn'] = False
                            print(f"LinkedIn upload error: {str(e)}")

                    # Handle Pinterest upload
                    if post.pinterest and third_party_data.pinterest_check:
                        try:
                            pin_image_array = []
                            for pin_data in all_files:
                                file_format = pin_data.split(".")[-1].split('.')[-1]
                                if file_format.lower() in image_formats:
                                    pin_image_array.append(pin_data)
                            
                            for upload_pin in pin_image_array:
                                pin_title = post.title if post.title else "Flowkar"
                                pin_description = post.description if post.description else "Flowkar"
                                pin_post, pin_id = post_pin(
                                    third_party_data.pinterest_creds, upload_pin, pin_title, pin_description)
                                if pin_post:
                                    post.pinterest_id = pin_id
                                    statuses['Pinterest'] = True
                                else:
                                    statuses['Pinterest'] = False
                            post.save()
                        except Exception as e:
                            statuses['Pinterest'] = False
                            print(f"Pinterest upload error: {str(e)}")

                    # Handle Vimeo upload
                    if post.vimeo and third_party_data.vimeo_check:
                        try:
                            vimeo_array = []
                            for vimeo_data in original_files:
                                file_format = vimeo_data.split(".")[-1].split('.')[-1]
                                if file_format.lower() in video_formats:
                                    vimeo_array.append(vimeo_data)
                            
                            for upload_vimeo in vimeo_array:
                                vimeo_title = post.title if post.title else "Flowkar"
                                vimeo_upload, vimeo_id = upload_vimeo_video(
                                    third_party_data.vimeo_creds, upload_vimeo, vimeo_title, post.description)
                                if vimeo_upload:
                                    post.vimeo_id = vimeo_id
                                    statuses['Vimeo'] = True
                                else:
                                    post.vimeo = False
                                    statuses['Vimeo'] = False
                            post.save()
                        except Exception as e:
                            statuses['Vimeo'] = False
                            print(f"Vimeo upload error: {str(e)}")

                    # Handle YouTube upload
                    if post.youtube and third_party_data.youtube_check:
                        try:
                            y_array = []
                            for y_data in original_files:
                                file_format = y_data.split(".")[-1].split('.')[-1]
                                if file_format.lower() in video_formats:
                                    y_array.append(y_data)

                            for upload_y in y_array:
                                y_title = post.title if post.title else "Flowkar"
                                pload, youtube_id = upload_video(
                                    creds=f'youtube/{post.brand_id}', file=upload_y, title=y_title, description=post.description)
                                if pload:
                                    post.youtube_id = youtube_id
                                    statuses['Youtube'] = True
                                else:
                                    post.youtube = False
                                    statuses['Youtube'] = False
                            post.save()
                        except Exception as e:
                            statuses['Youtube'] = False
                            print(f"YouTube upload error: {str(e)}")

                    # Handle TikTok upload
                    if post.dailymotion and third_party_data.tiktok_check:
                        try:
                            all_images = []
                            all_video = []
                            upload = False
                            for tiktok in all_files:
                                if tiktok.startswith("http://"):
                                    tiktok = tiktok.replace("http://", "https://", 1)
                                file_format = tiktok.split(".")[-1].split('.')[-1]
                                if file_format.lower() in image_formats:
                                    all_images.append(tiktok)
                                else:
                                    all_video.append(tiktok)
                            
                            if len(all_video) > 0:
                                upload, tiktok_upload_id_video = tiktok_upload_videos(
                                    third_party_data.tiktok_access_token, all_video, post.description)
                                if upload:
                                    post.tiktok_id = tiktok_upload_id_video
                                    statuses['TikTok'] = True
                                else:
                                    post.tiktok = False
                                    statuses['TikTok'] = False
                            post.save()
                        except Exception as e:
                            statuses['TikTok'] = False
                            print(f"TikTok upload error: {str(e)}")

                    # Handle Twitter/X upload
                    if post.twitter and third_party_data.thread_check:
                        try:
                            all_images = []
                            all_video = []
                            upload = False
                            for thread_files in all_files:
                                file_format = thread_files.split(".")[-1].split('.')[-1]
                                if file_format.lower() in image_formats:
                                    all_images.append(thread_files)
                                else:
                                    all_video.append(thread_files)
                            
                            if len(all_video) > 0:
                                upload, thread_upload_id_video = upload_and_publish_thread_video(
                                    third_party_data.thread_user_id, third_party_data.thread_auth_token, all_video[0], post.description)
                                if upload:
                                    post.twitter_id = thread_upload_id_video

                            if len(all_images) > 0:
                                upload, thread_upload_id = upload_images_to_thread(
                                    third_party_data.thread_user_id, third_party_data.thread_auth_token, all_images, post.description)
                                if upload:
                                    post.twitter_id = thread_upload_id
                            
                            statuses['Thread'] = upload
                            post.save()
                        except Exception as e:
                            statuses['Thread'] = False
                            print(f"Thread upload error: {str(e)}")

                    # Handle X (Twitter) upload
                    if post.x and third_party_data.x_check:
                        try:
                            access_token = get_and_update_x_token(third_party_data.x_refresh_token, post.brand_id)
                            all_images = []
                            all_video = []
                            upload = False
                            
                            for x_files in original_files:
                                file_format = x_files.split(".")[-1].split('.')[-1]
                                if file_format.lower() in image_formats:
                                    all_images.append(x_files)
                                else:
                                    all_video.append(x_files)
                            
                            if len(all_video) > 0:
                                upload, x_upload_id_video = upload_video_and_post_tweet(
                                    access_token, all_video, post.description)
                                if upload:
                                    post.x_id = x_upload_id_video
                            
                            if len(all_images) > 0:
                                upload, x_upload_id_image = post_tweet_with_asset(
                                    access_token, all_images, post.description)
                                if upload:
                                    post.x_id = x_upload_id_image
                            
                            statuses['X'] = upload
                            post.save()
                        except Exception as e:
                            statuses['X'] = False
                            print(f"X upload error: {str(e)}")

                    # Handle Tumblr upload
                    if post.tumblr and third_party_data.tumblr_check:
                        try:
                            all_images = []
                            all_video = []
                            for file in original_files:
                                file_format = file.split(".")[-1]
                                if file_format.lower() in video_formats:
                                    all_video.append(file)
                                elif file_format.lower() in image_formats:
                                    all_images.append(file)
                            
                            if len(all_images) > 0:
                                upload_tumblr, check_tumblr = upload_tumblr_photo(
                                    third_party_data.tumbler_token, third_party_data.tumbler_secret, all_images, post.description)
                                if check_tumblr:
                                    post.tumblr_id = upload_tumblr
                                    statuses['Tumblr'] = True
                                else:
                                    post.tumblr = False
                                    statuses['Tumblr'] = False
                            
                            if len(all_video) > 0:
                                for fi in all_video:
                                    upload_tumblr, check_tumblr = upload_tumblr_video(
                                        third_party_data.tumbler_token, third_party_data.tumbler_secret, fi, post.description)
                                    if check_tumblr:
                                        post.tumblr_id = upload_tumblr
                                        statuses['Tumblr'] = True
                                    else:
                                        post.tumblr = False
                                        statuses['Tumblr'] = False
                            post.save()
                        except Exception as e:
                            statuses['Tumblr'] = False
                            print(f"Tumblr upload error: {str(e)}")

                    # Handle Reddit upload
                    if post.reddit and third_party_data.reddit_check:
                        try:
                            if len(original_files) > 0:
                                upload_reddit, check_reddit = upload_photo_to_reddit(
                                    third_party_data.reddit_token, post.description, f'{header}{domain}/{original_files[0]}')
                                if check_reddit:
                                    statuses['Reddit'] = True
                                else:
                                    post.reddit = False
                                    statuses['Reddit'] = False
                            post.save()
                        except Exception as e:
                            statuses['Reddit'] = False
                            print(f"Reddit upload error: {str(e)}")

                    # Send notification to user
                    # notify = send_notification('Post uploaded Successfully', 'Flowkar',
                    #                        [post.user.onesignal_player])
                    # print(notify)

                    # Update points if needed
                    social_validated_data = {
                        'facebook': post.facebook,
                        'instagram': post.instagram,
                        'linkedin': post.linkedin,
                        'pinterest': post.pinterest,
                        'vimeo': post.vimeo,
                        'youtube': post.youtube,
                        'tiktok': post.dailymotion,
                        'threads': post.twitter,
                        'tumblr': post.tumblr,
                        'reddit': post.reddit
                    }
                    
                    is_any_post_social = check_platform_posts_last_24h(post.brand_id, social_validated_data)
                    social_referal_points = db_get_points("social_post_upload")
                    
                    for platform, is_allowed in is_any_post_social.items():
                        if is_allowed:
                            db_update_points(post.user_id, social_referal_points[0], "Social Post Upload Reward Credited")

                    is_any_post = check_user_posts_24h(post.brand_id)
                    if not is_any_post:
                        referal_points = db_get_points("upload_post")
                        db_update_points(post.user_id, referal_points[0], "Post Upload Reward Credited")

        except Exception as e:
            print(f"Error processing post {post_id}: {str(e)}")
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

        return Response({'status': True, 'message': 'Post Approved & Uploaded Successfully', 'data': statuses}, status=status.HTTP_200_OK)

class RejectPostUpload(APIView):
    authentication_classes = [CustomJWTAuthentication]
    def post(self, request):
        auth_token = request.headers.get('Authorization')
        token_user_id = decode_token(auth_token)
        post_id = request.data.get('post_id')
        
        try:
            post = Post.objects.get(pk=post_id)
            post.is_deleted = True
            post.is_approved = True
            post.is_posted = False
            post.save()
            return Response({'status': True, 'message': 'Post Rejected Successfully'}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

#Test Permissions
class TestPermissionsView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    def get(self, request):
        logged_in_user = request.headers.get('user')
        brand_id = int(request.headers.get('brand'))
        try:
            print(logged_in_user,brand_id)
            user_permissions = UserManagement.objects.get(user_invited__id=logged_in_user,brand_id=[brand_id])
            roles  = UserRoles.objects.get(pk=int(user_permissions.permission))
            permission_dict  = roles.role_description
            print(permission_dict)
            permission_list = extract_permission_keys(permission_dict)
            print(permission_list)
        except UserManagement.DoesNotExist:
            permission_list = [7]
        return Response({'status': True, 'message': 'Permissions Data Successfully Fetched','data':permission_list}, status=status.HTTP_200_OK)


# Like Profile Views
class LikeUserProfileUrlView(APIView):
    def get(self, request):
        try:
            post_id = request.data.get('post_id')
            if not post_id:
                return Response({'status': False, 'message': 'Post ID is required'}, status=status.HTTP_400_BAD_REQUEST)

            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''

            post = Post.objects.filter(id=post_id, is_deleted=False).first()
            if not post:
                return Response({'status': False, 'message': 'Post not found'}, status=status.HTTP_400_BAD_REQUEST)

            share_url = f'{header}{domain}/api/like-profile-view/{post_id}'
            return Response({'status': True, 'message': 'Like profile link generated', 'share_url': share_url}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class LikeUserProfileView(APIView):
    def get(self, request, id):
        try:
            post = Post.objects.filter(pk=id, is_deleted=False).first()
            if not post:
                return JsonResponse({'status': False, 'message': 'Post not found'}, status=status.HTTP_400_BAD_REQUEST)

            try:
                domain = request.get_host()
                if '127.0.0.1' in domain:
                    header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                header = ''
                domain = ''

            encrypted_post_id = encrypt_data(str(id))
            share_type = "like"

            html_content = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Flowkar - Like Post</title>
          <style>
            body {{ font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f0f0f0; }}
            .btn {{ display: inline-block; padding: 15px 30px; margin: 10px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }}
            .store {{ background: #000; }}
          </style>
        </head>
        <body>
          <h1>Opening Flowkar App...</h1>
          <p>If the app doesn't open automatically, please click the button below:</p>
          <a href="flowkar://api.flowkar.com/api/post/share/{id}?share_type={share_type}" class="btn">Open in Flowkar</a>
          <a href="https://apps.apple.com/app/flowkar/6740058663" class="btn store">Download App</a>

          <script>
            var appUrl = "flowkar://api.flowkar.com/api/post/share/{id}?share_type={share_type}";
            var fallbackUrl = "https://flowkar.com/get-post/?post_id={encrypted_post_id}";
            window.location.href = appUrl;
            setTimeout(function() {{
              if (!document.hidden) {{
                window.location.href = fallbackUrl;
              }}
            }}, 2000);
          </script>
        </body>
        </html>
        """
            return HttpResponse(html_content, content_type='text/html')
        except Exception as e:
            return JsonResponse({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


class LikeUserProfileDeeplinkView(View):
    def get(self, request, post_id):
        post_id = escape(post_id)

        html_content = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Flowkar - Like Post</title>
          <style>
            body {{ font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f0f0f0; }}
            .btn {{ display: inline-block; padding: 15px 30px; margin: 10px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }}
            .store {{ background: #000; }}
          </style>
        </head>
        <body>
          <h1>Opening Flowkar App...</h1>
          <p>If the app doesn't open automatically, please click the button below:</p>
          <a href="flowkar://api.flowkar.com/api/like-profile-view/{post_id}" class="btn">Open in Flowkar</a>
          <a href="https://apps.apple.com/app/flowkar/6740058663" class="btn store">Download App</a>

          <script>
            window.location.href = "flowkar://api.flowkar.com/api/like-profile-view/{post_id}";
            setTimeout(function() {{
              if (document.visibilityState === "visible") {{
                if (/iPhone|iPad|iPod/.test(navigator.userAgent)) {{
                  window.location.href = "https://apps.apple.com/app/flowkar/6740058663";
                }} else {{
                  window.location.href = "https://play.google.com/store/apps/details?id=com.app.flowkar";
                }}
              }}
            }}, 2000);
          </script>
        </body>
        </html>
        """
        return HttpResponse(html_content, content_type='text/html')


# Markent Views
class CreateMarketView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            token_user_id = decode_token(auth_token)
            category = request.data.get('category')
            event_name = request.data.get('event_name')
            maximum_joiners = request.data.get('maximum_joiners')
            minimum_followers = request.data.get('minimum_followers')
            selected_social_platforms = request.data.get('selected_social_platforms',[])
            language_preference = request.data.get('language_preference',[])
            registration_max_time = request.data.get('registration_max_time')
            maximum_pool_running_days = request.data.get('maximum_pool_running_days')
            event_prize = request.data.get('event_prize')
            max_event_rank = request.data.get('max_event_rank')
            brand_name = request.data.get('brand_name')
            brand_logo = request.FILES.get('brand_logo')
            brand_description = request.data.get('brand_description')
            content_requirements = request.data.get('content_requirements')
            product_description = request.data.get('product_description')
            hashtags = request.data.get('hashtags',[])
            guidelines = request.data.get('guidelines')
            required_fields = [category,event_name,maximum_joiners,minimum_followers,registration_max_time,maximum_pool_running_days,event_prize,max_event_rank,brand_name,brand_logo,brand_description,content_requirements,product_description,guidelines]
            for field in required_fields:
                if not field:
                    return Response({'status': False, 'message': f'{field} is required'}, status=status.HTTP_400_BAD_REQUEST)
            
            try:
                user = UserRegistration.objects.get(id=token_user_id)
                if user.user_type != 'Business Owner':
                    return Response({'status': False, 'message': 'User is not a Business Owner'}, status=status.HTTP_400_BAD_REQUEST)
                if not user.is_kyc_verified:
                    return Response({'status': False, 'message': 'User is not KYC verified'}, status=status.HTTP_400_BAD_REQUEST)
                
            except UserRegistration.DoesNotExist:
                return Response({'status': False, 'message': 'User not found'}, status=status.HTTP_400_BAD_REQUEST)
            
            try:
                create_market = FantasyEvent.objects.create(
                    user=user,
                    category=category,
                    event_name=event_name,
                    maximum_joiners=maximum_joiners,
                    minimum_followers=minimum_followers,
                    selected_social_platforms=selected_social_platforms,
                    language_preference=language_preference,
                    registration_max_time=registration_max_time,
                    maximum_pool_running_days=maximum_pool_running_days,
                    event_prize=event_prize,
                    max_event_rank=max_event_rank,
                    brand_name=brand_name,
                    brand_logo=brand_logo,
                    brand_description=brand_description,
                    content_requirements=content_requirements,
                    product_description=product_description,
                    hashtags=hashtags,
                    guidelines=guidelines
                )
                return Response({'status': True, 'message': 'Market created successfully'}, status=status.HTTP_200_OK)
            except Exception as e:
                return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

class GetMarketView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    def get(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            token_user_id = decode_token(auth_token)
            user = UserRegistration.objects.get(id=token_user_id)
            if user.user_type != 'Business Owner':
                return Response({'status': False, 'message': 'User is not a Business Owner'}, status=status.HTTP_400_BAD_REQUEST)
            markets = FantasyEvent.objects.filter(user_id=token_user_id).order_by('-created_at')
            market_data = []
            for market in markets:
                market_data.append({
                    'id': market.id,
                    'event_name': market.event_name,
                    'category': market.category.name,
                    'maximum_joiners': market.maximum_joiners,
                    'minimum_followers': market.minimum_followers,
                    'selected_social_platforms': market.selected_social_platforms,
                    'language_preference': market.language_preference,
                    'registration_max_time': market.registration_max_time,
                    'maximum_pool_running_days': market.maximum_pool_running_days,
                    'event_prize': market.event_prize,
                    'max_event_rank': market.max_event_rank,
                })
            return Response({'status': True, 'message': 'Markets fetched successfully', 'data': market_data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

class GetMarketDetailsView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    def get(self, request):
        try:
            try:
                markent_id = request.data.get('markent_id')
                if not markent_id:
                    return Response({'status': False, 'message': 'Markent ID is required'}, status=status.HTTP_400_BAD_REQUEST)
                markent = FantasyEvent.objects.get(id=markent_id)
                markent_data = {
                    'id': markent.id,
                    'event_name': markent.event_name,
                    'category': markent.category.name,
                    'maximum_joiners': markent.maximum_joiners,
                    'minimum_followers': markent.minimum_followers,
                    'selected_social_platforms': markent.selected_social_platforms,
                    'language_preference': markent.language_preference,
                    'registration_max_time': markent.registration_max_time,
                    'maximum_pool_running_days': markent.maximum_pool_running_days,
                    'event_prize': markent.event_prize,
                    'max_event_rank': markent.max_event_rank,
                    'brand_name': markent.brand_name,
                    'brand_logo': markent.brand_logo.url if markent.brand_logo else '',
                    'brand_description': markent.brand_description,
                    'content_requirements': markent.content_requirements,
                    'product_description': markent.product_description,
                    'hashtags': markent.hashtags,
                    'guidelines': markent.guidelines
                }
                return Response({'status': True, 'message': 'Markent fetched successfully', 'data': markent_data}, status=status.HTTP_200_OK)
            except FantasyEvent.DoesNotExist:
                return Response({'status': False, 'message': 'Markent not found'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

class GetMarkentCategoriesView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    def get(self, request):
        try:
            categories = MarkentCategories.objects.all()
            category_data = []
            for category in categories:
                category_data.append({
                    'id': category.id,
                    'name': category.name
                })
            return Response({'status': True, 'message': 'Markent categories fetched successfully', 'data': category_data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)


#KYC Views

class GenerateAdharOTPView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            token_user_id = decode_token(auth_token)
            aadhar_number = request.data.get('aadhar_number')
            aadhar_front_image = request.FILES.get('aadhar_front_image')
            aadhar_back_image = request.FILES.get('aadhar_back_image')

            try:
                user_kyc = UserKYC.objects.get(user_id=token_user_id)
                if user_kyc:
                    user_kyc.aadhar_front_image = aadhar_front_image
                    user_kyc.aadhar_back_image = aadhar_back_image
                    user_kyc.save()
                    otp_status,otp_data = kyc_generate_adhar_otp(aadhar_number)
                    if otp_status:
                        return Response({'status': True, 'message': 'OTP generated successfully','refference_id':otp_data}, status=status.HTTP_200_OK)
                    else:
                        return Response({'status': False, 'message': 'OTP generation failed,Please check your details'}, status=status.HTTP_400_BAD_REQUEST)
            except UserKYC.DoesNotExist:
                create = UserKYC.objects.create(
                    user_id=token_user_id,
                    aadhar_number=aadhar_number,
                    aadhar_front_image=aadhar_front_image,
                    aadhar_back_image=aadhar_back_image,
                )

                otp_status,otp_data = kyc_generate_adhar_otp(aadhar_number)
                if otp_status:
                        return Response({'status': True, 'message': 'OTP generated successfully','refference_id':otp_data}, status=status.HTTP_200_OK)
                else:
                    return Response({'status': False, 'message': 'OTP generation failed,Please check your details'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

class VerifyAdharOtpView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            token_user_id = decode_token(auth_token)
            refference_id = request.data.get('refference_id')
            otp = request.data.get('otp')
            if not refference_id or not otp:
                return Response({'status': False, 'message': 'Refference ID and OTP are required'}, status=status.HTTP_400_BAD_REQUEST)
            otp_status,otp_data = kyc_verify_adhar_otp(refference_id,otp)
            if otp_status:
                user_kyc = UserKYC.objects.get(user_id=token_user_id)
                user_kyc.is_verified_aadhar = True
                user_kyc.save()
                return Response({'status': True, 'message': 'OTP verified successfully','is_verified_aadhar':True}, status=status.HTTP_200_OK)
            else:
                return Response({'status': False, 'message': 'OTP verification failed,Please check your OTP','data':otp_data}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

class VerifyPanDetailsView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            token_user_id = decode_token(auth_token)
            pan_number = request.data.get('pan_number')
            name = request.data.get('name')
            dob = request.data.get('dob')
            pan_image = request.FILES.get('pan_image')
            if not pan_number or not name or not dob:
                return Response({'status': False, 'message': 'Pan number, name and dob are required'}, status=status.HTTP_400_BAD_REQUEST)
            user_kyc = UserKYC.objects.get(user_id=token_user_id)
            if user_kyc.user.user_type != 'Business Owner':
                is_additional_details_added = True
            else:
                is_additional_details_added = False

            pan_status,pan_data = verify_pan_details(pan_number,name,dob)

            if pan_status:
                user_kyc.is_verified_pan = True
                user_kyc.name_as_per_pan = pan_data['name_as_per_pan']
                user_kyc.pan_image = pan_image
                if user_kyc.is_verified_aadhar and user_kyc.is_verified_pan:
                    user_kyc.user.is_kyc_verified = True
                    user_kyc.user.save()
                user_kyc.save()
                return Response({'status': True, 'message': 'Pan details verified successfully','is_verified_pan':True,'data':pan_data,'is_additional_details_added':is_additional_details_added}, status=status.HTTP_200_OK)
            else:
                return Response({'status': False, 'message': 'Pan details verification failed,Please check your details','data':pan_data,'is_additional_details_added':is_additional_details_added}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)

class CreateAdditionalDetailsView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    def post(self, request):
        try:
            auth_token = request.headers.get('Authorization')
            token_user_id = decode_token(auth_token)
            location = request.data.get('location',{})
            language_preference = request.data.get('language_preference',[])
            industries_preference = request.data.get('industries_preference',[])

            try:
                user_info = JoinerAdditionalDetails.objects.get(user_id=token_user_id)
                return Response({'status': True, 'message': 'Additional details already added'}, status=status.HTTP_200_OK)
            except JoinerAdditionalDetails.DoesNotExist:
                create = JoinerAdditionalDetails.objects.create(
                    user_id=token_user_id,
                    location=location,
                    language_preference=language_preference,
                    industries_preference=industries_preference
                )
                return Response({'status': True, 'message': 'Additional details added successfully'}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)